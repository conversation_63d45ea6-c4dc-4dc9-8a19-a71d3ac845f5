<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interest Calculator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
            display: flex; /* Sidebar + content side by side */
        }
        /* Sidebar styles */
        .sidebar {
            width: 250px;
            background: #1e293b;
            color: #fff;
            height: 100vh;
            padding: 20px 15px;
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
        }
        .sidebar-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .sidebar-header img {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        .nav-menu {
            list-style: none;
            padding: 0;
        }
        .nav-item {
            margin-bottom: 10px;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            color: #fff;
            text-decoration: none;
            transition: background 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: #334155;
        }
        .nav-icon {
            margin-right: 10px;
        }
        /* Adjusted styles for centering */
        .content {
            flex-grow: 1; /* Allow content to grow and take remaining space */
            display: flex; /* Use flexbox to center the child element */
            justify-content: center; /* Center horizontally */
            align-items: center; /* Center vertically */
            padding: 20px;
            margin-left: 250px; /* Keep the margin for the sidebar */
        }
        .centered-container {
            max-width: 960px; /* Constrain the width of the form */
            width: 100%; /* Ensure it takes full width up to max-width */
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
        }
        input[type="date"]::-webkit-calendar-picker-indicator {
            cursor: pointer;
        }
        .form-input {
            @apply block w-full px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-150 ease-in-out;
        }
        .form-label {
            @apply block text-sm font-medium text-blue-800 mb-1;
        }
        .read-only {
            @apply bg-blue-50 text-blue-900 border-blue-400 font-bold;
        }
        .text-header-details {
            font-size: 11px;
            color: #666;
            line-height: 1.2;
        }
        .input-group {
            @apply flex flex-col;
        }
        .section-box {
            @apply bg-white p-6 rounded-2xl shadow-lg border border-gray-200;
        }
        .section-header {
            @apply text-xl sm:text-2xl font-bold text-center text-red-600 mb-6;
        }

        /* New Print-Specific Styles */
        .print-voucher-container {
            display: none; /* Initially hidden */
        }
        .print-voucher-box {
            border: 1px solid black;
            padding: 20px;
        }
        .print-voucher-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .print-voucher-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .print-voucher-row strong {
            display: inline-block;
            width: 150px;
        }
        .print-voucher-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .print-voucher-table th, .print-voucher-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }
        .print-voucher-table th {
            background-color: #f2f2f2;
        }

        /* CSS for printing */
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                margin: 0;
                padding: 0;
                background: none;
                height: 297mm; /* A4 height */
                width: 210mm;  /* A4 width */
            }
            .max-w-4xl {
                display: none; /* Hide the main calculator interface */
            }
            .print-voucher-wrapper {
                display: flex;
                flex-direction: column;
                justify-content: space-between; /* Pushes one voucher to top, one to bottom */
                align-items: center;
                height: 100%; /* Take full A4 height */
                padding: 10mm; /* A little margin for the cutter */
                box-sizing: border-box;
                page-break-after: always; /* Ensure it prints on separate pages if needed */
            }
            .print-voucher-container {
                display: block !important;
                width: 800% !important;
                max-width: 200mm !important; /* A little less than A4 width for margin */
                border: 1px solid #000;
                border-radius: 0;
                font-size: 15px !important;px;
                box-shadow: none;
                margin: 0;
                padding: 10px;
                box-sizing: border-box;
                page-break-inside: avoid; /* Prevent breaking inside the voucher */
            }
            /* Add a dashed line separator for easy cutting */
            .print-voucher-wrapper::before {
                content: "";
                display: block;
                width: 0%;
                border-top: 1px dashed #999;
                margin: 10mm 0;
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                z-index: 1;
                pointer-events: none;
            }
            .print-voucher-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }
            .print-voucher-title {
                font-size: 18px !important;
                margin: 0;
                text-align: center;
                font-weight: bold;
                text-transform: uppercase;
                flex-grow: 1;
                margin-left: 20px;
                margin-right: 20px;
            }
            .print-voucher-logo {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                object-fit: cover;
                margin-right: 10px;
                margin-left: 10px;
                float: left;
                margin-top: -10px;
                margin-bottom: -10px;
                position: relative;
                top: -10px;
                left: -10px;
                z-index: 2;
                border: 2px solid #000;
                background-color: #fff;
                padding: 2px;
                box-sizing: border-box;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); /* Optional shadow for better visibility */
                float: left;
                margin-right: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar no-print">
        <div class="sidebar-header">
            <img src="20250608_130850.png" alt="Logo">
            <h3>AMMAN GOLD FINANCE</h3>
        </div>

        <ul class="nav-menu">
            <li class="nav-item">
                <a href="main.html#loan-section" class="nav-link">
                    <span class="nav-icon">💰</span>
                    Loan Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="interestcreation.html" class="nav-link active">
                    <span class="nav-icon">📊</span>
                    Interest Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="Termsandcondition.html" class="nav-link">
                    <span class="nav-icon">📋</span>
                    Terms And Condition
                </a>
            </li>
            <li class="nav-item">
                <a href="#" onclick="logout(); return false;" class="nav-link">
                    <span class="nav-icon">🚪</span>
                    Logout
                </a>
            </li>
        </ul>
    </div>

    <div class="content">
        <div class="max-w-4xl w-full bg-white p-6 sm:p-10 rounded-3xl shadow-2xl border-2 border-gray-100 space-y-8 no-print centered-container">
            <header class="flex items-center justify-start space-x-4">
                <img src="20250608_130850.png" alt="Amman Gold Finance Logo" class="mr-20 w-20 h-20 rounded-xl">
                <div class="flex-grow">
                    <h1 class="text-xl sm:text-2xl font-bold text-red-600 mb-2" style="color: #ad0202; text-align: center;">AMMAN GOLD FINANCE</h1>
                    <p class="text-size: 12px; font-bold text-blue-1000 mb-2" style="color: #000000;" class="text-header-details">
                    </p>
                </div>
            </header>

            <div class="section-box"><b><br>
                <h2 class="section-header color:darkblue-600" style="color: #1c02ad ; text-align: center;">INTEREST RECEIPT</h2>
                    </br>
                <div id="calculator-form" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                    <div class="input-group">
                        <label for="loan-id" class="form-label">Customer Loan ID:</label>
                        <input type="text" id="loan-id" value="CUST-001" class="form-input">
                    </div>

                    <div class="input-group">
                        <label for="customer-name" class="form-label">Customer Name:</label>
                        <input type="text" id="customer-name" value="" class="form-input">
                    </div>

                    <div class="input-group">
                        <label for="loan-amount" class="form-label">Loan Amount:</label>
                        <input type="number" id="loan-amount" value="0" min="0" class="form-input">
                    </div>

                    <div class="input-group no-print"><br>
                        <label for="loan-date" class="form-label">Loan Date:</label></br>
                        <input type="date" id="loan-date" class="form-input" value="2024-07-20">
                    </div>

                    <div class="input-group no-print"><br>
                        <label for="last-till-date" class="form-label">Last Till Date:</label></br>
                        <input type="date" id="last-till-date" class="form-input">
                    </div>

                    <div class="input-group"><br>
                        <label for="number-of-days" class="form-label">No of Days:</label></br>
                        <input type="number" id="number-of-days" value="0" min="0" class="form-input read-only" readonly>
                    </div>

                    <div class="input-group no-print">
                        <label for="roi" class="form-label">ROI (Rate of Interest):</label>
                        <select id="roi" class="form-input">
                            <option value="12">12% (Rs.1.00)</option>
                            <option value="15" selected>15% (Rs.1.25)</option>
                            <option value="18">18% (Rs.1.50)</option>
                            <option value="22">22% (Rs.1.75)</option>
                            <option value="24">24% (Rs.2.00)</option>
                            <option value="30">30% (Rs.2.50)</option>
                            <option value="36">36% (Rs.3.00)</option>
                            <option value="48">48% (Rs.4.00)</option>
                        </select>
                    </div>

                    <div class="input-group no-print">
                        <label for="partial-interest" class="form-label">Partial Interest:</label>
                        <input type="number" id="partial-interest" value="0" min="0" class="form-input">
                    </div>

                    <div class="input-group no-print">
                        <label for="paid-interest" class="form-label">Paid Interest:</label>
                        <input type="number" id="paid-interest" value="0.00" min="0" class="form-input">
                    </div>

                    <div class="input-group">
                        <label for="current-interest" class="form-label">Current Interest:</label>
                        <input type="number" id="current-interest" value="0.00" class="form-input read-only" readonly>
                    </div>

                    <div class="input-group no-print">
                        <label for="paid-principal" class="form-label">Reducing Amount:</label>
                        <input type="number" id="paid-principal" value="0" min="0" class="form-input">
                    </div>

                    <div class="input-group">
                        <label for="paid-principal-display" class="form-label">Paid Principal:</label>
                        <input type="number" id="paid-principal-display" value="0" class="form-input read-only" readonly>
                    </div>

                    <div class="input-group">
                        <label for="total-amount" class="form-label">Total Amount:</label>
                        <input type="number" id="total-amount" value="0.00" class="form-input read-only" readonly>
                    </div>

                    <div class="input-group">
                        <label for="new-principal-balance" class="form-label">New Principal Balance:</label>
                        <input type="number" id="new-principal-balance" value="0.00" class="form-input read-only" readonly>
                    </div>
                </div>
            </div>

            <div class="section-box"><br>
                <h2 class="section-header">Amount in Words</h2></br>
                <div class="grid grid-cols-1 gap-4">
                    <div class="input-group">
                        <label for="total-amount-in-words" class="form-label">Total Amount in Words:</label>
                        <div id="total-amount-in-words" class="p-4 bg-blue-50 border border-blue-400 rounded-lg shadow-inner text-lg font-semibold text-blue-900">
                            Zero Rupees Only
                        </div>
                    </div>
                </div>
            </div>
            <div class="section-box no-print">
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <button id="close-btn" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-8 rounded-full shadow-lg transition transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-gray-300">
                        Close
                    </button>
                    <button id="print-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-full shadow-lg transition transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-blue-300">
                        Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="print-voucher-wrapper">
        <div id="print-voucher-top" class="print-voucher-container">
            <h1 class="print-voucher-title">CASH RECEIPT VOUCHER</h1>
            <div class="print-voucher-box">
                <div class="print-voucher-header flex justify-between items-center mb-4">
                    <div>
                        <b><h2 class="text-left text-1xl font-bold color:darkblue-600">AMMAN GOLD FINANCE</h2></b>
                        <p>REGISTERED OFFICE: NO 2/4-4,</p>
                        <p>S.V.A. EXTENSION - 4,</p>
                        <p>OPPOSITE GOVERNMENT GIRL'S HIGH SCHOOL,</p>
                        <P>TIRUCHENGODE - 637211, NAMAKKAL DISTRICT.</P>
                        <p>Phone: +91 8608183335</p>
                        <p>Email: <EMAIL></p>
                    </div>
                    <div class="text-right">
                        <b><p>Customer Name: <span id="print-customer-name-top"></span></p></b>
                        <p>Loan No: <span id="print-loan-id-top"></span></p>
                        <p>Date: <span id="print-date-top"></span></p>
                        <p>Cash Receipt No: <span id="print-cash-receipt-no-top"></span></p>
                    </div>
                </div>

                <table class="print-voucher-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Interest Paid</td>
                            <td>₹ <span id="print-interest-paid-top"></span></td>
                        </tr>
                        <tr>
                            <td>Paid Principal</td>
                            <td>₹ <span id="print-paid-principal-top"></span></td>
                        </tr>
                        <tr>
                            <td>Total in words</td>
                            <td><span id="print-total-in-words-top"></span></td>
                        </tr>
                        <tr>
                            <td>Paid To</td>
                            <td>AMMAN GOLD FINANCE</td>
                        </tr>
                        <tr class="no-print">
                            <td>Interest Type</td>
                            <td><span id="print-interest-type-top"></span></td>
                        </tr>
                        <tr>
                            <td>Interest Days</td>
                            <td><span id="print-interest-days-top"></span> Days</td>
                        </tr>
                        <tr>
                            <td>Till Date</td>
                            <td><span id="print-till-date-top"></span></td>
                        </tr>
                        <tr>
                            <td>Balance Principal</td>
                            <td><span id="print-balance-principal-top"></span></td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt-8 flex justify-between text-sm">
                    <p>Paid by</p>
                    <p>Verified by</p>
                </div>
            </div>
        </div>

        <div id="print-voucher-bottom" class="print-voucher-container">
            <h1 class="print-voucher-title">CASH RECEIPT VOUCHER</h1>
            <div class="print-voucher-box">
                <div class="print-voucher-header flex justify-between items-center mb-4">
                    <div>
                        <b><h2 class="text-left text-1xl font-bold color:darkblue-600">AMMAN GOLD FINANCE</h2></b>
                        <p>REGISTERED OFFICE: NO 2/4-4,</p>
                        <p>S.V.A. EXTENSION - 4,</p>
                        <p>OPPOSITE GOVERNMENT GIRL'S HIGH SCHOOL,</p>
                        <P>TIRUCHENGODE - 637211, NAMAKKAL DISTRICT.</P>
                        <p>Phone: +91 8608183335</p>
                        <p>Email: <EMAIL></p>
                    </div>
                    <div class="text-right">
                        <b><p>Customer Name: <span id="print-customer-name-bottom"></span></p></b>
                        <p>Loan No: <span id="print-loan-id-bottom"></span></p>
                        <p>Date: <span id="print-date-bottom"></span></p>
                        <p>Cash Receipt No: <span id="print-cash-receipt-no-bottom"></span></p>
                    </div>
                </div>

                <table class="print-voucher-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Interest Paid</td>
                            <td>₹ <span id="print-interest-paid-bottom"></span></td>
                        </tr>
                        <tr>
                            <td>Paid Principal</td>
                            <td>₹ <span id="print-paid-principal-bottom"></span></td>
                        </tr>
                        <tr>
                            <td>Total in words</td>
                            <td><span id="print-total-in-words-bottom"></span></td>
                        </tr>
                        <tr>
                            <td>Paid To</td>
                            <td>AMMAN GOLD FINANCE</td>
                        </tr>
                        <tr class="no-print">
                            <td>Interest Type</td>
                            <td><span id="print-interest-type-bottom"></span></td>
                        </tr>
                        <tr>
                            <td>Interest Days</td>
                            <td><span id="print-interest-days-bottom"></span> Days</td>
                        </tr>
                        <tr>
                            <td>Till Date</td>
                            <td><span id="print-till-date-bottom"></span></td>
                        </tr>
                        <tr>
                            <td>Balance Principal</td>
                            <td><span id="print-balance-principal-bottom"></span></td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt-8 flex justify-between text-sm">
                    <p>Paid by</p>
                    <p>Verified by</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gold Financial Interest Calculator
        class GoldFinancialCalculator {
            constructor() {}

            // Converts a number into Indian currency words
            formatAmountInWords(amount) {
                const num = amount.toFixed(2).split('.');
                let rupees = parseInt(num[0], 10);
                let paise = parseInt(num[1], 10);

                if (rupees === 0 && paise === 0) return 'Zero Rupees Only';

                const a = ['', 'One ', 'Two ', 'Three ', 'Four ', 'Five ', 'Six ', 'Seven ', 'Eight ', 'Nine '];
                const b = ['', 'Ten ', 'Twenty ', 'Thirty ', 'Forty ', 'Fifty ', 'Sixty ', 'Seventy ', 'Eighty ', 'Ninety '];
                const c = ['', 'Eleven ', 'Twelve ', 'Thirteen ', 'Fourteen ', 'Fifteen ', 'Sixteen ', 'Seventeen ', 'Eighteen ', 'Nineteen '];

                function inWords(n) {
                    let s = '';
                    if (n >= 10000000) {
                        s += inWords(Math.floor(n / 10000000)) + 'Crore '; // Crores
                        n %= 10000000;
                    }
                    if (n >= 100000) {
                        s += inWords(Math.floor(n / 100000)) + 'Lakh '; // Lakhs
                        n %= 100000;
                    }
                    if (n >= 1000) {
                        s += inWords(Math.floor(n / 1000)) + 'Thousand '; // Thousands
                        n %= 1000;
                    }
                    if (n >= 100) {
                        s += a[Math.floor(n / 100)] + 'Hundred '; // Hundreds
                        n %= 100;
                    }
                    if (n >= 10) {
                        s += b[Math.floor(n / 10)]; // Tens
                        n %= 10;
                    }
                    
                    if (n >= 20) {
                        s += b[Math.floor(n / 10)]; // Tens
                        s += a[n % 10];
                    } else if (n >= 11) {
                        s += c[n - 10]; // Teens
                    } else if (n >= 1) {
                        s += a[n]; // Units
                    }
                    return s.trim();
                }

                let words = '';
                if (rupees > 0) {
                    words += inWords(rupees).trim() + ' Rupees ';
                }
                if (paise > 0) {
                    const paiseWords = inWords(paise);
                    if (words !== '') {
                        words += ' and ';
                    }
                    words += paiseWords.trim() + ' Paise';
                }

                if (words.trim() !== '') {
                    return words.trim() + ' Only';
                }
                return 'Zero Rupees Only';
            }

            // Main calculation function based on the provided formula
            calculateInterest(loanAmount, numberOfDays, roiPercentage) {
                if (loanAmount < 0 || numberOfDays < 0 || roiPercentage < 0) {
                    return 0;
                }
                const interest = (loanAmount * numberOfDays / 365) * (roiPercentage / 100);
                return Math.round(interest * 100) / 100;
            }

            // A single function to perform all calculations and return the results
            calculateComplete(loanAmount, numberOfDays, roiPercentage, partialInterestPaid = 0, paidPrincipalAmount = 0) {
                const totalInterest = this.calculateInterest(loanAmount, numberOfDays, roiPercentage);
                const newPrincipalBalance = Math.max(0, loanAmount - paidPrincipalAmount);
                const currentInterest = Math.max(0, totalInterest - partialInterestPaid);
                const totalAmount = newPrincipalBalance + currentInterest;

                return {
                    totalInterest: totalInterest,
                    currentInterest: currentInterest,
                    newPrincipalBalance: newPrincipalBalance,
                    totalAmount: totalAmount
                };
            }
        }

        // Function to generate a unique cash receipt number
        function generateCashReceiptNumber() {
            const date = new Date();
            const year = date.getFullYear().toString().slice(-2); // Last 2 digits of year
            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month with leading zero
            const day = date.getDate().toString().padStart(2, '0'); // Day with leading zero
            const random = Math.floor(1000 + Math.random() * 9000); // 4-digit random number
            return `CR${year}${month}${day}${random}`;
        }

        // Event listener setup
        window.onload = function() {
            const calculator = new GoldFinancialCalculator();

            // Get all input elements that trigger a calculation
            const loanAmountInput = document.getElementById('loan-amount');
            const numberOfDaysInput = document.getElementById('number-of-days');
            const roiSelect = document.getElementById('roi');
            const partialInterestInput = document.getElementById('partial-interest');
            const paidPrincipalInput = document.getElementById('paid-principal');
            const paidInterestInput = document.getElementById('paid-interest');

            // Get all display elements
            const paidPrincipalDisplay = document.getElementById('paid-principal-display');
            const currentInterestDisplay = document.getElementById('current-interest');
            const newPrincipalBalanceDisplay = document.getElementById('new-principal-balance');
            const totalAmountDisplay = document.getElementById('total-amount');
            const totalAmountInWordsDisplay = document.getElementById('total-amount-in-words');

            // Get date inputs for automatic day calculation
            const loanDateInput = document.getElementById('loan-date');
            const lastTillDateInput = document.getElementById('last-till-date');

            // Get print voucher display elements
            const voucherIDs = ['top', 'bottom'];

            function performCalculation() {
                const loanAmount = parseFloat(loanAmountInput.value) || 0;
                const numberOfDays = parseFloat(numberOfDaysInput.value) || 0;
                const roiPercentage = parseFloat(roiSelect.value) || 0;
                const partialInterest = parseFloat(partialInterestInput.value) || 0;
                const paidPrincipal = parseFloat(paidPrincipalInput.value) || 0;
                const paidInterest = parseFloat(paidInterestInput.value) || 0;

                // Update the "Paid Principal" display field to match the editable "Reducing Amount" field
                paidPrincipalDisplay.value = paidPrincipal.toFixed(2);

                // Perform the complete calculation
                const result = calculator.calculateComplete(
                    loanAmount,
                    numberOfDays,
                    roiPercentage,
                    partialInterest,
                    paidPrincipal
                );

                // Update the UI with the calculated values
                currentInterestDisplay.value = result.currentInterest.toFixed(2);
                newPrincipalBalanceDisplay.value = result.newPrincipalBalance.toFixed(2);

                // Calculate the Total Amount based on paid principal and paid interest
                const totalPaidAmount = paidPrincipal + paidInterest;
                totalAmountDisplay.value = totalPaidAmount.toFixed(2);

                // Update the "Total Amount in Words" with the new total
                totalAmountInWordsDisplay.textContent = calculator.formatAmountInWords(totalPaidAmount);
            }

            // Function to calculate days between two dates
            function calculateDays() {
                // Ensure the date inputs have a value before proceeding
                if (!loanDateInput.value || !lastTillDateInput.value) {
                    // Update number of days and total amount to 0 if dates are not selected
                    numberOfDaysInput.value = 0;
                    totalAmountDisplay.value = 0;
                    return;
                }
                const loanDate = new Date(loanDateInput.value);
                const lastTillDate = new Date(lastTillDateInput.value);
                const timeDifference = lastTillDate.getTime() - loanDate.getTime();
                const dayDifference = timeDifference / (1000 * 3600 * 24);
                numberOfDaysInput.value = Math.floor(dayDifference);
                // After updating the days, trigger the main calculation
                performCalculation();
            }

            // Function to update print voucher content
            function updatePrintVoucher() {
                const customerName = document.getElementById('customer-name').value;
                const loanId = document.getElementById('loan-id').value;
                const paidInterest = parseFloat(document.getElementById('paid-interest').value) || 0;
                const paidPrincipal = parseFloat(document.getElementById('paid-principal').value) || 0;
                const totalAmountInWords = document.getElementById('total-amount-in-words').textContent;
                const roiText = document.getElementById('roi').options[document.getElementById('roi').selectedIndex].text;
                const interestDays = document.getElementById('number-of-days').value;
                const tillDate = document.getElementById('last-till-date').value;
                const balancePrincipal = document.getElementById('new-principal-balance').value;
                const cashReceiptNo = generateCashReceiptNumber();

                voucherIDs.forEach(id => {
                    document.getElementById(`print-customer-name-${id}`).textContent = customerName;
                    document.getElementById(`print-loan-id-${id}`).textContent = loanId;
                    document.getElementById(`print-date-${id}`).textContent = new Date().toLocaleDateString('en-GB');
                    document.getElementById(`print-cash-receipt-no-${id}`).textContent = cashReceiptNo;
                    document.getElementById(`print-interest-paid-${id}`).textContent = paidInterest.toFixed(2);
                    document.getElementById(`print-paid-principal-${id}`).textContent = paidPrincipal.toFixed(2);
                    document.getElementById(`print-total-in-words-${id}`).textContent = totalAmountInWords;
                    document.getElementById(`print-interest-type-${id}`).textContent = roiText;
                    document.getElementById(`print-interest-days-${id}`).textContent = interestDays;
                    document.getElementById(`print-till-date-${id}`).textContent = tillDate;
                    document.getElementById(`print-balance-principal-${id}`).textContent = balancePrincipal;
                });
            }

            // Attach event listeners to trigger recalculation and voucher update
            loanAmountInput.addEventListener('input', performCalculation);
            roiSelect.addEventListener('change', performCalculation);
            partialInterestInput.addEventListener('input', performCalculation);
            paidPrincipalInput.addEventListener('input', performCalculation);
            paidInterestInput.addEventListener('input', performCalculation);
            loanDateInput.addEventListener('change', calculateDays);
            lastTillDateInput.addEventListener('change', calculateDays);

            // Print button functionality
            document.getElementById('print-btn').addEventListener('click', function() {
                updatePrintVoucher();
                window.print();
            });

            // Close button functionality (optional, but good practice)
            document.getElementById('close-btn').addEventListener('click', function() {
                window.location.href = 'main.html';
            });

            // Initial calculation on page load
            calculateDays();
        };

        /**
         * Logs the user out of the application and redirects them to the login page.
         * 
         * This function is called whenever the user needs to be logged out,
         * such as when they click the logout button. It informs the user that
         * they are being logged out, and then redirects them to the login page.
         * 
         * @return {void}
         */
        function logout() {
            // Inform the user that they are being logged out
            // This is a courtesy to the user, so they are not surprised when
            // they are redirected to the login page
            alert('Logging out...');

            // Redirect to login page
            // If the user is logged in, this will redirect them to the login
            // page, otherwise it will redirect them to the home page
            window.location.href = 'login.html';
        }
    </script>
</body>
</html>