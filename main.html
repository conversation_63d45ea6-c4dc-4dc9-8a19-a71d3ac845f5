<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loan Creation - Amman Gold Finance</title>
    <link rel="shortcut icon" href="20250608_130850.png" type="image/x-icon">
    <style>
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 999;
        }

        .sidebar-header {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 30px;
        }

        .sidebar-header img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            color: #ecf0f1;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #3498db;
        }

        .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: white;
            border-left-color: #3498db;
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
        }

        .content-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .content-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }

        /* Interest Calculation Modal Style */
        .interest-modal {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            margin: 0 auto;
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px 30px;
            position: relative;
        }

        .modal-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .close-btn {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-input {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            color: #495057;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-input:disabled {
            background: #e9ecef;
            color: #6c757d;
        }

        .description-input {
            min-height: 80px;
            resize: vertical;
        }

        .modal-footer {
            padding: 20px 30px;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .btn-cancel {
            background: #6c757d;
            color: white;
        }

        .btn-cancel:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-save {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-save:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
            
            .main-content {
                margin-left: 200px;
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .modal-body {
                padding: 20px;
            }
        }

        @media (max-width: 600px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .nav-menu {
                display: flex;
                overflow-x: auto;
            }
            
            .nav-item {
                margin-bottom: 0;
                margin-right: 5px;
            }

            .interest-modal {
                margin: 10px;
            }

            .modal-footer {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }

        /* Print Styles */
        @media print {
            .sidebar {
                display: none !important;
            }
            
            .main-content {
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="20250608_130850.png" alt="Logo">
            <h3>AMMAN GOLD FINANCE</h3>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="main.html#loan-section" class="nav-link active">
                    <span class="nav-icon">💰</span>
                    Loan Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="interestcreation.html" class="nav-link">
                    <span class="nav-icon">📊</span>
                    Interest Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="Termsandcondition.html" class="nav-link">
                    <span class="nav-icon">📋</span>
                    Terms And Condition
                </a>
            </li>
            <li class="nav-item">
                <a href="javascript:void(0);" onclick="logout(); return false;" class="nav-link">
                    <span class="nav-icon">🚪</span>
                    Logout
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="loan-section">
        <div class="content-header">
            <div class="header-flex">
                <div class="header-logo">
                    <img src="20250608_130850.png" alt="Logo"></div>
            <h1>AMMAN GOLD FINANCE
                <text style class center="font-size: 16px; font-weight: bold;">  
            <br>REGISTERED OFFICE: NO 2/4-4, S.V.A. EXTENSION - 4,
                    <br>OPPOSITE GOVERNMENT GIRL'S HIGH SCHOOL TIRUCHENGODE,
                     <br> NAMAKKAL DISTRICT- 637211.<br>
                    <br>Phone: +91 8608183335
                    Email: <EMAIL></br></text></h1>
        </div>
        </div>
        <!-- Loan Creation Modal -->
       
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="script.js"></script>
    <script src="camera.js"></script>
    <script src="jewelry.js"></script>
    <script src="customer.js"></script>
    <script src="loan.js"></script>
    <script src="images.js"></script>
    <script src="totals.js"></script>
    <script src="print.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-reset.js"></script>
    <script src="form-submit.js"></script>
    <script src="form-compact.js"></script>
    <script src="form-responsive.js"></script>
    <script src="form-print.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>  
    <script src="form-enhancements.js"></script>
    <script src="form-compatibility.js"></script>
    <script src="form-accessibility.js"></script>
    <script src="form-optimization.js"></script>
    <script src="form-customization.js"></script>
    <script src="form-integration.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-analytics.js"></script>
    <script src="form-security.js"></script>
    <script src="form-performance.js"></script>
    <script src="form-a11y.js"></script>
    <script src="form-ux.js"></script>
    <script src="form-ui.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
    <script src="form-enhancements.js"></script>    
    <style>
        /* General styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
            color: #e70505;
            line-height: 1.6;
            font-size: 20px;
            -webkit-print-color-adjust: exact; /* Ensure colors are printed correctly */
            -moz-print-color-adjust: exact; /* Ensure colors are printed correctly */
            print-color-adjust: exact; /* Ensure colors are printed correctly */
            background: linear-gradient(to right, #f0f0f0, #ffffff);
            color: #c90a0a; /* Dark red color for text */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            box-sizing: border-box; /* Ensure padding and borders are included in element's total width and height */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            font-size: 20px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            text-align: left; /* Align text to the left */
            padding: 20px; /* Add padding to the body */
            margin: 0; /* Remove default margin */
            background-color: #f8f9fa; /* Light background color */
            color: #000000; /* Dark text color for better contrast */
            text-align: left; /* Align text to the left */
            line-height: 1.5; /* Set line height for readability */
            font-size: 16px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            text :bold; /* Ensure text is not transformed */
            font-family: 'Helvetica Neue', Arial, sans-serif; /* Use a clean sans-serif font */
        }

           @media print {
            .container {
                border: 2px solid #333 !important;   /* Dark outline */
                border-radius: 10px !important;
                margin: 20px auto !important;        /* Margin around the box */
                padding: 16px !important;
                background: #fff !important;
                box-shadow: none !important;
                max-width: 95% !important;
            }
        }   
        h1,
        h2 {
            text-align: center;
        }

        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }

        .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 10px;
            max-width: 800px;
            margin: 0 auto 10px auto;
            padding: 0 15px;
        }

        .image-item {
            width: 220px;
            min-width: 200px;
            max-width: 240px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 15px 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .image-item label {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            width: 100%;
        }

        .image-item input[type="file"] {
            display: block;
            margin: 0 auto 4px auto;
            width: 100%;
            box-sizing: border-box;
            position: static;
            font-size: 11px;
            padding: 3px;
        }

        .image-item label {
            font-weight: bold;
            margin-bottom: 6px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }

        /* Customer Details Compact Layout */
        .customer-details-compact {
            max-width: 3000px;
            min-width: 100px;
            width: 100%;
            height: 0.5;
            background: #f9f9f9;
            margin: 0 auto;
            padding: 15px;
        }

        .customer-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 10px;
        }

        .customer-column-left,
        .customer-column-right {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .customer-details-grid .form-group {
            margin-bottom: 0;
        }

        .customer-details-grid .form-group label {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .customer-details-grid .form-group input,
        .customer-details-grid .form-group textarea {
            padding: 8px;
            font-size: 14px;
        }

        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .customer-details-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .customer-details-compact {
                padding: 12px;
            }
        }

        /* Loan Information Compact Layout */
        .loan-info-compact {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .loan-info-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .loan-row-top {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .loan-row-bottom {
            display: flex;
            width: 100%;
        }

        .amount-words-group {
            width: 100%;
        }

        .amount-words {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            min-height: 20px;
            font-style: italic;
            color: #666;
        }

        .loan-info-compact .form-group {
            margin-bottom: 0;
        }

        .loan-info-compact .form-group label {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .loan-info-compact .form-group input {
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        /* Responsive design for loan info */
        @media (max-width: 1200px) {
            .loan-row-top {
                grid-template-columns: 1fr 1fr 1fr;
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            .loan-row-top {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .loan-row-top {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        .file-info {
            font-size: 10px;
            color: #888;
            margin-bottom: 4px;
            width: 100%;
            text-align: center;
            border-radius: 4px;
            background: #f9f9f9;
            padding: 3px 0;
            line-height: 1.2;
        }

        .image-preview-box {
            margin-top: 8px;
            width: 100%;
            height: 150px;
            min-height: 150px;
            max-height: 150px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            box-shadow: 0 2px 4px rgb(255, 0, 0);
            padding: 5px;
            box-sizing: border-box;
            overflow: hidden;
        }

        #customerPhotoPreview,
        .jewelry-preview-img {
            max-width: 100% !important;
            max-height: 100% !important;
            width: 100% !important;
            height: 100% !important;
            object-fit: contain !important;
            border-radius: 6px;
            background: #fff;
            display: block;
            border: 1px solid #ccc;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            margin: 0 auto;
            padding: 2px;
            box-sizing: border-box;
        }
        .image-preview-box img:hover {
            transform: scale(1.05);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-preview-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
            transition: all 0.2s ease;
        }

        /* Camera Controls */
        .camera-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 6px;
            width: 100%;
        }

        .camera-controls input[type="file"] {
            width: 100%;
            font-size: 11px;
            padding: 4px;
        }

        .camera-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
            align-self: center;
            width: fit-content;
        }

        .camera-btn:hover {
            background: #0056b3;
        }

        /* Camera Modal */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .camera-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-header h3 {
            margin: 0;
            color: #333;
        }

        .close-camera {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-camera:hover {
            color: #000;
        }

        #customerCameraVideo,
        #jewelryCameraVideo {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .camera-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .capture-btn,
        .retake-btn,
        .save-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .capture-btn {
            background: #28a745;
            color: white;
        }

        .retake-btn {
            background: #ffc107;
            color: #212529;
        }

        .save-btn {
            background: #007bff;
            color: white;
        }

        .capture-btn:hover {
            background: #218838;
        }

        .retake-btn:hover {
            background: #e0a800;
        }

        .save-btn:hover {
            background: #0056b3;
        }

        /* Images Section Compact Layout */
        .images-section-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .images-section-compact h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: #e70303;
        }

        /* Responsive design for images section */
        @media (max-width: 768px) {
            .image-container-box {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                width: 100%;
                padding: 0 10px;
                margin: 0 auto;
                max-width: 90%;
            }

            .images-section-compact {
                padding: 12px;
            }
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
            color: #999;
            font-size: 12px;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #f10000;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #fafafc;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #ad0202;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 10mm;
    }

    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    .container {
        width: 190mm !important;
        max-width: 190mm !important;
        margin: 0 auto !important;
        padding: 5mm !important;
        text-align: center !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Center all form elements */
    .section-box {
        margin: 2mm auto !important;
        padding: 2mm !important;
        text-align: left !important;
        width: 100% !important;
    }

    /* Center header */
    .header-flex {
        justify-content: center !important;
        text-align: center !important;
        margin-bottom: 3mm !important;
    }

    /* Center images section */
    .image-container-box {
        justify-content: center !important;
        margin: 0 auto !important;
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
        .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    
    @media print {
        body {
            font-size: 8px !important; /* Adjust as needed */
        }
        .container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 5mm !important; /* Adjust as needed */
        }
        .header-flex {
            margin-bottom: 3mm !important;
        }
        .header-logo img {
            width: 20mm !important;
            height: 20mm !important;
        }
        .section-box {
            margin-bottom: 2mm !important;
            padding: 2mm !important;
        }
        .form-group {
            margin-bottom: 1mm !important;
        }
        .form-group label {
            margin-bottom: 0.5mm !important;
        }
        .image-preview-box img,
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 15mm !important;
            height: 15mm !important;
        }
        .weight-row {
            gap: 1mm !important;
        }
        .weight-item {
            padding: 1mm !important;
        }
        .weight-item label {
            font-size: 7px !important;
        }
        .weight-item input {
            font-size: 7px !important;
        }
    }
    </style>
</head>
<style>
</style>
    <div class="container">
        <!-- Customer Information -->
        <div class="section-box customer-details-compact">
            <!-- Customer Details -->
            <h2>Customer Details</h2>
            <div class="customer-details-grid">
                <!-- Left Column -->
                <div class="customer-column-left">
                    <div class="form-group">
                        <label for="customerId">Customer ID:</label>
                        <input type="text" id="customerId" placeholder="CUST-001">
                    </div>
                    <div class="form-group">
                        <label for="name">Full Name:</label>
                        <input type="text" id="name" placeholder="John Doe">
                    </div>
                    <div class="form-group">
                        <label for="address">Address:</label>
                        <textarea id="address" rows="2" placeholder="123 Gold Street, City"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="pincode">Pincode:</label>
                        <input type="text" id="pincode" placeholder="123456">
                    </div>
                    <div class="form-group">
                        <label for="gender">Gender:</label>
                        <select id="gender">
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="customer-column-right">
                    <div class="form-group">
                        <label for="idProof">ID Proof:</label>
                        <input type="text" id="idProof" placeholder="Aadhar Card / Voter ID / Pan Card / Passport">
                    </div>
                    <div class="form-group">
                        <label for="dob">Date of Birth:</label>
                        <input type="date" id="dob">
                    </div>
                    <div class="form-group">
                        <label for="contact">Contact Number:</label>
                        <input type="tel" id="contact" placeholder="1234567890">
                    </div>
                    <div class="form-group">
                        <label for="landmark">Landmark:</label>
                        <input type="text" id="landmark" placeholder="Landmark">
                    </div>
                    <div class="form-group">
                        <label for="occupation">Occupation:</label>
                        <input type="text" id="occupation" placeholder="Occupation">
                    </div>

                </div>
            </div>
        </div>
           
          <!-- Loan Information Section -->
            <div class="loan-info-compact">
                <div class="loan-info-grid">
                    <!-- Top Row - Loan Amount and Dates -->
                    <div class="loan-row-top">
                        <div class="form-group">
                            <label for="loanAmount">Loan Amount:</label>
                            <input type="number" id="loanAmount" placeholder="0.00" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="loanDate">Loan Date:</label>
                            <input type="date" id="loanDate">
                        </div>
                        <div class="form-group">
                            <label for="renewalDate">Last Renewal Date:</label>
                            <input type="date" id="renewalDate">
                        </div>
                        <div class="form-group">
                            <label for="Schemename">Scheme Name</label>
                            <select id="Scheme Name">
                                <option value="select">Select</option>
                                <option value="AGL 0.90 Paise">AGL 0.90 Paise</option>
                                <option value="AGL 1.00 RUPEE">AGL 1.00 RUPEE</option>
                                <option value="MGL 1.25 RUPEE">MGL 1.25 RUPEE</option>
                                <option value="HGL 1.50 RUPEE">HGL 1.50 RUPEE</option>
                                <option value="HGL+ 1.80 RUPEE">HGL+ 1.80 RUPEE</option>
                                <option value="EGL 2.00 RUPEE">EGL 2.00 RUPEE</option>
                                <option value="EGL 2.10 RUPEE">EGL 2.10 RUPEE</option>
                                <option value="EGL+ 2.50 RUPEE">EGL+ 2.50 RUPEE</option>
                                <option value="EGL++ 3.00 RUPEE">EGL++ 3.00 RUPEE</option>
                            </select>
                        </div>
                    </div>

                    <!-- Bottom Row - Amount in Words -->
                    <div class="loan-row-bottom">
                        <div class="form-group amount-words-group">
                            <label>Amount in Words:</label>
                            <div id="loanAmountWords" class="amount-words"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Image upload section -->
        <div class="container">
        <div class="section-box images-section-compact">
            <h2>Images</h2>
            <div class="image-container-box">
                <!-- Customer Photo -->
                <div class="image-item">
                    <label>Customer Photo:</label>
                    <div class="camera-controls">
                        <input type="file" id="customerPhotoInput" accept="image/*">
                        <button type="button" id="customerCameraBtn" class="camera-btn">📷 Camera</button>
                    </div>
                    <div id="customerFileInfo" class="file-info">No file chosen</div>

                    <!-- Camera Modal -->
                    <div id="customerCameraModal" class="camera-modal" style="display: none;">
                        <div class="camera-content">
                            <div class="camera-header">
                                <h3>Take Customer Photo</h3>
                                <button type="button" id="customerCloseCamera" class="close-camera">×</button>
                            </div>
                            <video id="customerCameraVideo" autoplay playsinline></video>
                            <canvas id="customerCameraCanvas" style="display: none;"></canvas>
                            <div class="camera-buttons">
                                <button type="button" id="customerCaptureBtn" class="capture-btn">📸 Capture</button>
                                <button type="button" id="customerRetakeBtn" class="retake-btn" style="display: none;">🔄 Retake</button>
                                <button type="button" id="customerSaveBtn" class="save-btn" style="display: none;">✓ Save</button>
                            </div>
                        </div>
                    </div>

                    <div class="image-preview-box">
                        <div id="customerPhotoPlaceholder" class="image-placeholder hide-on-print">
                            <span style="font-size: 7px;">Upload Customer Photo</span>
                        </div>
                        <img id="customerPhotoPreview" alt="Customer Photo" style="display: none;">
                    </div>
                </div>
                
                <!-- Jewelry Photos -->
                <div class="image-item">
                    <label>Jewelry Photos:</label>
                    <div class="camera-controls">
                        <input type="file" id="jewelryPhotoInput" accept="image/*" multiple>
                        <button type="button" id="jewelryCameraBtn" class="camera-btn">📷 Camera</button>
                    </div>
                    <div id="jewelryFileInfo" class="file-info">No file chosen</div>

                    <!-- Camera Modal -->
                    <div id="jewelryCameraModal" class="camera-modal" style="display: none;">
                        <div class="camera-content">
                            <div class="camera-header">
                                <h3>Take Jewelry Photo</h3>
                                <button type="button" id="jewelryCloseCamera" class="close-camera">×</button>
                            </div>
                            <video id="jewelryCameraVideo" autoplay playsinline></video>
                            <canvas id="jewelryCameraCanvas" style="display: none;"></canvas>
                            <div class="camera-buttons">
                                <button type="button" id="jewelryCaptureBtn" class="capture-btn">📸 Capture</button>
                                <button type="button" id="jewelryRetakeBtn" class="retake-btn" style="display: none;">🔄 Retake</button>
                                <button type="button" id="jewelrySaveBtn" class="save-btn" style="display: none;">✓ Save</button>
                            </div>
                        </div>
                    </div>

                    <div class="image-preview-box">
                        <div id="jewelryPreviewContainer">
                            <div id="jewelryPhotoPlaceholder" class ="image-placeholder hide-on-print">
                                <span style="font-size: 7px;">Upload Jewelry Images</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Single Jewelry Details section with proper structure -->
        <div class="section-box">
            <h2>Jewelry Details</h2>
            <div id="jewelryItemsList">
                <!-- Initial jewelry item will be added here by JavaScript -->
            </div>
            <!-- Totals calculation row -->
              <div class="jewelry-totals">
                <h3>Totals</h3>
                <div class="weight-row totals-row">
                    <div class="weight-item">
                        <label for="totalJewelryCount">Total Jewelry Count:</label>
                        <input type="number" id="totalJewelryCount" readonly>
                    </div>
                    <div class="weight-item">
                        <label for="totalJewelryWeight">Total Weight (grams):</label>

                        <input type="number" id="totalJewelryWeight" readonly>
                    </div>
                    <div class="weight-item">
                        <label for="totalStoneWeight">Total Stone Weight (grams):</label>
                        <input type="number" id="totalStoneWeight" readonly>
                    </div>
                    <div class="weight-item">
                        <label for="totalNetWeight">Total Net Weight (grams):</label>
                        <input type="number" id="totalNetWeight" readonly>
                    </div>
                </div>
            </div>
            
            <div class="add-item-container">
                <button type="button" id="addJewelryItemBtn" class="add-item-btn hide-on-print">Add Jewelry Item</button>
            </div>
        </div>
              <!-- Place this after the totals section, before </div> of .container -->
        <!-- Print buttons -->
        <div class="section-box">
            <div class="print-buttons-container">
                <button class="print-btn office-copy" onclick="printDocument('office')">Office Copy</button>
                <button class="print-btn customer-copy" onclick="printDocument('customer')">Customer Copy</button>
            </div>
        </div>
        </div>
    <script>
        // Event listeners with DOM ready check
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize jewelry items functionality
            initializeJewelryItemsFunctionality();
            
            const addBtn = document.getElementById('addJewelryItemBtn');
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    console.log("Add Jewelry Item button clicked");
                    addJewelryItem();

        // Use event delegation for dynamically created elements
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-item-btn')) {
                const index = parseInt(e.target.getAttribute('data-index'));
                removeJewelryItem(index);
            }
        });

        // Use event delegation for weight inputs
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('jewelry-weight') || 
                e.target.classList.contains('stone-weight') || 
                e.target.classList.contains('jewelry-count')) {
                console.log("Input change event triggered");
                calculateNetWeightForItem(e.target);
                calculateTotals();
            }
        });

        // Function to calculate net weight for a specific item
        function calculateNetWeightForItem(inputElement) {
            const jewelryItem = inputElement.closest('.jewelry-item');
            if (!jewelryItem) return;
            
            const jewelryWeight = parseFloat(jewelryItem.querySelector('.jewelry-weight')?.value) || 0;
            const stoneWeight = parseFloat(jewelryItem.querySelector('.stone-weight')?.value) || 0;
            const netWeightInput = jewelryItem.querySelector('.net-weight');
            
            if (netWeightInput) {
                const netWeight = jewelryWeight - stoneWeight;
                netWeightInput.value = netWeight >= 0 ? netWeight.toFixed(1) : '0';

            if (netWeight >= 0) {
                document.getElementById('netWeight').value = netWeight.toFixed(2);
            }
        }

        // Set default date to today for renewal date
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default for renewal date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10); // Format: YYYY-MM-DD
            document.getElementById('renewalDate').value = formattedDate;
            
            // Format loan amount with currency symbol on blur
            const loanAmountInput = document.getElementById('loanAmount');
            loanAmountInput.addEventListener('blur', function() {
                if (this.value) {
                    // Format with 2 decimal places
                    const formattedValue = parseFloat(this.value).toFixed(2);
                    this.value = formattedValue;
                    
                    // Update amount in words
                    const amountInWords = numberToWords(parseFloat(this.value));
                    document.getElementById('loanAmountWords').textContent = amountInWords;
                }
            });
            
            // Auto-set renewal date to one year after loan date
            const loanDateInput = document.getElementById('loanDate');
            loanDateInput.addEventListener('change', function() {
                if (this.value) {
                    const loanDate = new Date(this.value);
                    // Add one year to the loan date
                    const renewalDate = new Date(loanDate);
                    renewalDate.setFullYear(renewalDate.getFullYear() + 1);
                    // Format and set the renewal date
                    const renewalDateFormatted = renewalDate.toISOString().substr(0, 10);
                    document.getElementById('renewalDate').value = renewalDateFormatted;
                }
            });
            
            // Customer photo preview
            const customerPhotoInput = document.getElementById('customerPhotoInput');
            const customerPhotoPreview = document.getElementById('customerPhotoPreview');
            const customerPhotoPlaceholder = document.getElementById('customerPhotoPlaceholder');
            const customerFileInfo = document.getElementById('customerFileInfo');
            
/* Screen display styles */
.simple-image-container {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    flex-direction: row;
}

.simple-image-item {
    flex: 1;
    text-align: center;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 8px;
}

.simple-image-item label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
}

.image-preview-area {
    width: 150px;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    border: 1px solid #ccc;
    background: #f9f9f9;
    overflow: hidden;
    box-sizing: border-box;
}

#customerPhotoPreview, #jewelryPhotoPreview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

        /* Print styles - jewelry left, customer right, properly contained */
        @media print {
            /* Hide all image containers and show only images */
            .image-container-box {
                display: flex !important;
                flex-direction: row !important;
                gap: 20px !important;
                margin: 10px 0 !important;
                justify-content: space-between !important;
                width: 100% !important;
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
            }
            
            /* Completely remove all styling from image items */
            .image-item {
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
                box-shadow: none !important;
                outline: none !important;
            }
            
            /* Jewelry photo container - LEFT SIDE */
            .image-item:nth-child(2) {
                order: 1 !important;
                flex: 1 !important;
                border: none !important;
                padding: 0 !important;
                border-radius: 0 !important;
                background: transparent !important;
                width: 45% !important;
                max-width: 45% !important;
                box-sizing: border-box !important;
                margin: 0 !important;
            }
            
            /* Customer photo container - RIGHT SIDE */
            .image-item:nth-child(1) {
                order: 2 !important;
                flex: 1 !important;
                padding: 0 !important;
                border-radius: 0 !important;
                background: transparent !important;
            }
            
            .image-preview-box {
                width: 100% !important;
                height: 120px !important;
                max-height: 120px !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                border: none !important;
            }
            
            /* Force both images to fit properly within their containers */
            #customerPhotoPreview, .jewelry-preview-img {
                width: 100% !important;
                height: 100% !important;
                max-width: 100% !important;
                max-height: 100% !important;
                object-fit: contain !important;
                display: block !important;
            }
            
            /* Remove borders from jewelry preview container */
            #jewelryPreviewContainer {
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            
            /* Remove borders from all image placeholders */
            .image-placeholder {
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            
            /* Remove ALL borders from image sections in print */
            .images-section-compact,
            .image-container-box,
            .image-item,
            .image-preview-box,
            #customerPhotoPreview,
            .jewelry-preview-img,
            #jewelryPreviewContainer,
            .image-placeholder,
            .customer-photo-section,
            .jewelry-photo-section {
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
                background: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            
            /* Specifically target any remaining borders */
            * {
                border-color: transparent !important;
            }
            
            .image-item *,
            .image-preview-box *,
            #jewelryPreviewContainer * {
                border: none !important;
                outline: none !important;
                box-shadow: none !important;
            }
        }
            // Check if there's already a file selected (for page reloads)
            if (customerPhotoInput.files && customerPhotoInput.files[0]) {
                handleCustomerPhotoSelection(customerPhotoInput.files[0]);
            }
            
            /**
             * Handle customer photo selection.
             * @param {File} file - The selected file.
             */
            customerPhotoInput.addEventListener('change', function(this: HTMLInputElement, e: Event) {
                const file: File | null = this.files[0];
                if (file) {
                    handleCustomerPhotoSelection(file);
                } else {
                    customerFileInfo.textContent = 'No file chosen';
                    customerPhotoPreview.style.display = 'none';
                    customerPhotoPlaceholder.style.display = 'flex';
                }
            });

            /**
             * Handle customer photo selection.
             * @param {File} file - The selected file.
             * @returns {void}
             */
            function handleCustomerPhotoSelection(file: File): void {
                customerFileInfo.textContent = file.name;
                const reader: FileReader = new FileReader();
                reader.onload = (this: FileReader, e: ProgressEvent): void => {
                    customerPhotoPreview.src = e.target.result;
                    customerPhotoPreview.style.display = 'block';
                    customerPhotoPlaceholder.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
            
            /**
             * Handle jewelry photo selection.
             * @param {FileList} files - The selected files.
             * @returns {void}
             */
            function handleJewelryPhotoSelection(files: FileList): void {
                const numFiles = files.length;
                const text = `${numFiles} file${numFiles === 1 ? '' : 's'} selected`;
                jewelryFileInfo.textContent = text;
                
                // Clear previous previews except placeholder
                const oldPreviews = Array.from(jewelryPreviewContainer.children).filter(child => child !== jewelryPhotoPlaceholder);
                oldPreviews.forEach(child => jewelryPreviewContainer.removeChild(child));
                
                // Add new previews
                Array.from(files).forEach((file: File) => {
                    const reader = new FileReader();
                    reader.onload = (event: ProgressEvent): void => {
                        const img = document.createElement('img');
                        img.src = event.target.result;
                        img.className = 'jewelry-preview-img';
                        jewelryPreviewContainer.appendChild(img);
                    };
                    reader.readAsDataURL(file);
            }
            }
    </script>

    <!-- Camera Functionality Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Camera functionality for Customer Photo
        const customerCameraBtn = document.getElementById('customerCameraBtn');
        const customerCameraModal = document.getElementById('customerCameraModal');
        const customerCloseCamera = document.getElementById('customerCloseCamera');
        const customerCameraVideo = document.getElementById('customerCameraVideo');
        const customerCameraCanvas = document.getElementById('customerCameraCanvas');
        const customerCaptureBtn = document.getElementById('customerCaptureBtn');
        const customerRetakeBtn = document.getElementById('customerRetakeBtn');
        const customerSaveBtn = document.getElementById('customerSaveBtn');

        let customerStream = null;
        let customerCapturedImage = null;

        // Open customer camera
        customerCameraBtn.addEventListener('click', async function() {
            try {
                customerStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                });
                customerCameraVideo.srcObject = customerStream;
                customerCameraModal.style.display = 'flex';
                customerCaptureBtn.style.display = 'inline-block';
                customerRetakeBtn.style.display = 'none';
                customerSaveBtn.style.display = 'none';
            } catch (err) {
                alert('Error accessing camera: ' + err.message);
            }
        });

        // Close customer camera
        customerCloseCamera.addEventListener('click', function() {
            if (customerStream) {
                customerStream.getTracks().forEach(track => track.stop());
            }
            customerCameraModal.style.display = 'none';
        });

        // Capture customer photo
        customerCaptureBtn.addEventListener('click', function() {
            const context = customerCameraCanvas.getContext('2d');
            customerCameraCanvas.width = customerCameraVideo.videoWidth;
            customerCameraCanvas.height = customerCameraVideo.videoHeight;
            context.drawImage(customerCameraVideo, 0, 0);

            customerCapturedImage = customerCameraCanvas.toDataURL('image/jpeg', 0.8);
            customerCameraVideo.style.display = 'none';
            customerCameraCanvas.style.display = 'block';

            customerCaptureBtn.style.display = 'none';
            customerRetakeBtn.style.display = 'inline-block';
            customerSaveBtn.style.display = 'inline-block';
        });

        // Retake customer photo
        customerRetakeBtn.addEventListener('click', function() {
            customerCameraVideo.style.display = 'block';
            customerCameraCanvas.style.display = 'none';
            customerCaptureBtn.style.display = 'inline-block';
            customerRetakeBtn.style.display = 'none';
            customerSaveBtn.style.display = 'none';
        });

        // Save customer photo
        customerSaveBtn.addEventListener('click', function() {
            if (customerCapturedImage) {
                const customerPhotoPreview = document.getElementById('customerPhotoPreview');
                const customerPhotoPlaceholder = document.getElementById('customerPhotoPlaceholder');
                const customerFileInfo = document.getElementById('customerFileInfo');

                customerPhotoPreview.src = customerCapturedImage;
                customerPhotoPreview.style.display = 'block';
                customerPhotoPlaceholder.style.display = 'none';
                customerFileInfo.textContent = 'Camera photo captured';

                // Stop camera stream
                if (customerStream) {
                    customerStream.getTracks().forEach(track => track.stop());
                }
                customerCameraModal.style.display = 'none';
            }
        });
    });
    </script>

    <!-- Jewelry Camera Functionality Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Camera functionality for Jewelry Photos
        const jewelryCameraBtn = document.getElementById('jewelryCameraBtn');
        const jewelryCameraModal = document.getElementById('jewelryCameraModal');
        const jewelryCloseCamera = document.getElementById('jewelryCloseCamera');
        const jewelryCameraVideo = document.getElementById('jewelryCameraVideo');
        const jewelryCameraCanvas = document.getElementById('jewelryCameraCanvas');
        const jewelryCaptureBtn = document.getElementById('jewelryCaptureBtn');
        const jewelryRetakeBtn = document.getElementById('jewelryRetakeBtn');
        const jewelrySaveBtn = document.getElementById('jewelrySaveBtn');

        let jewelryStream = null;
        let jewelryCapturedImage = null;

        // Open jewelry camera
        jewelryCameraBtn.addEventListener('click', async function() {
            try {
                jewelryStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'environment' // Use back camera for jewelry
                    }
                });
                jewelryCameraVideo.srcObject = jewelryStream;
                jewelryCameraModal.style.display = 'flex';
                jewelryCaptureBtn.style.display = 'inline-block';
                jewelryRetakeBtn.style.display = 'none';
                jewelrySaveBtn.style.display = 'none';
            } catch (err) {
                // Fallback to front camera if back camera not available
                try {
                    jewelryStream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'user'
                        }
                    });
                    jewelryCameraVideo.srcObject = jewelryStream;
                    jewelryCameraModal.style.display = 'flex';
                } catch (fallbackErr) {
                    alert('Error accessing camera: ' + fallbackErr.message);
                }
            }
        });

        // Close jewelry camera
        jewelryCloseCamera.addEventListener('click', function() {
            if (jewelryStream) {
                jewelryStream.getTracks().forEach(track => track.stop());
            }
            jewelryCameraModal.style.display = 'none';
        });

        // Capture jewelry photo
        jewelryCaptureBtn.addEventListener('click', function() {
            const context = jewelryCameraCanvas.getContext('2d');
            jewelryCameraCanvas.width = jewelryCameraVideo.videoWidth;
            jewelryCameraCanvas.height = jewelryCameraVideo.videoHeight;
            context.drawImage(jewelryCameraVideo, 0, 0);

            jewelryCapturedImage = jewelryCameraCanvas.toDataURL('image/jpeg', 0.8);
            jewelryCameraVideo.style.display = 'none';
            jewelryCameraCanvas.style.display = 'block';

            jewelryCaptureBtn.style.display = 'none';
            jewelryRetakeBtn.style.display = 'inline-block';
            jewelrySaveBtn.style.display = 'inline-block';
        });

        // Retake jewelry photo
        jewelryRetakeBtn.addEventListener('click', function() {
            jewelryCameraVideo.style.display = 'block';
            jewelryCameraCanvas.style.display = 'none';
            jewelryCaptureBtn.style.display = 'inline-block';
            jewelryRetakeBtn.style.display = 'none';
            jewelrySaveBtn.style.display = 'none';
        });

        // Save jewelry photo
        jewelrySaveBtn.addEventListener('click', function() {
            if (jewelryCapturedImage) {
                const jewelryPreviewContainer = document.getElementById('jewelryPreviewContainer');
                const jewelryPhotoPlaceholder = document.getElementById('jewelryPhotoPlaceholder');
                const jewelryFileInfo = document.getElementById('jewelryFileInfo');

                // Create new image element for jewelry
                const jewelryImg = document.createElement('img');
                jewelryImg.src = jewelryCapturedImage;
                jewelryImg.className = 'jewelry-preview-img';
                jewelryImg.alt = 'Jewelry Photo';

                // Clear existing content and add new image
                jewelryPreviewContainer.innerHTML = '';
                jewelryPreviewContainer.appendChild(jewelryImg);
                jewelryFileInfo.textContent = 'Camera photo captured';

                // Stop camera stream
                if (jewelryStream) {
                    jewelryStream.getTracks().forEach(track => track.stop());
                }
                jewelryCameraModal.style.display = 'none';
            }
        });
    });
    </script>

    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Customer Photo Preview
    const customerPhotoInput = document.getElementById('customerPhotoInput');
    const customerPhotoPreview = document.getElementById('customerPhotoPreview');
    const customerPhotoPlaceholder = document.getElementById('customerPhotoPlaceholder');
    const customerFileInfo = document.getElementById('customerFileInfo');

    customerPhotoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            customerFileInfo.textContent = file.name;
            const reader = new FileReader();
            reader.onload = function(e) {
                customerPhotoPreview.src = e.target.result;
                customerPhotoPreview.style.display = 'block';
                customerPhotoPlaceholder.style.display = 'none';
            };
            reader.readAsDataURL(file);
        } else {
            customerFileInfo.textContent = 'No file chosen';
            customerPhotoPreview.style.display = 'none';
            customerPhotoPlaceholder.style.display = 'flex';
        }
    });

    // Jewelry Photos Preview
    const jewelryPhotoInput = document.getElementById('jewelryPhotoInput');
    const jewelryPreviewContainer = document.getElementById('jewelryPreviewContainer');
    const jewelryPhotoPlaceholder = document.getElementById('jewelryPhotoPlaceholder');
    const jewelryFileInfo = document.getElementById('jewelryFileInfo');

    jewelryPhotoInput.addEventListener('change', function() {
        // Remove old previews except placeholder
        Array.from(jewelryPreviewContainer.children).forEach(child => {
            if (child !== jewelryPhotoPlaceholder) {
                jewelryPreviewContainer.removeChild(child);
            }
        });

        if (this.files.length > 0) {
            jewelryFileInfo.textContent = this.files.length + ' file(s) selected';
            jewelryPhotoPlaceholder.style.display = 'none';
            Array.from(this.files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'jewelry-preview-img';
                    jewelryPreviewContainer.appendChild(img);
                };
                reader.readAsDataURL(file);
            });
        } else {
            jewelryFileInfo.textContent = 'No file chosen';
            jewelryPhotoPlaceholder.style.display = 'flex';
        }
    });
});
</script>
    <style>
        /* Weight row styling */
        .weight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: space-between;
            width: 100%;
            box-sizing: border-box;
        }

        .weight-item {
            flex: 1;
            min-width: 150px;
            max-width: 200px;
            box-sizing: border-box;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            margin-bottom: 10px;
            /* Ensure content doesn't overflow */
            overflow: hidden;
        }

        .weight-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
            /* Remove duplicate styling */
            width: 100%;
            box-sizing: border-box;
        }

        .weight-item input {
            width: 100%;
            box-sizing: border-box;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            /* Ensure input stays within container */
            max-width: 100%;
        }

        /* Print styles for weight items */
        @media print {
            .weight-row {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 10px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
            
            .weight-item {
                flex: 1 !important;
                min-width: 0 !important;
                max-width: 33% !important;
                box-sizing: border-box !important;
                padding: 5px !important;
            }
            
            .weight-item input {
                width: 100% !important;
                box-sizing: border-box !important;
                padding: 4px !important;
            }
        }

        /* Jewelry type row styling */
        .jewelry-type-row {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
            align-items: flex-end;
        }

        .jewelry-selection, .purity-selection {
            flex: 1;
            min-width: 150px;
        }

        /* Hide signatures on screen, show only in print */
        .signature-row {
            display: none !important;
            flex-direction: row !important;
        }

        /* Print buttons styling */
        .print-buttons-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }

        .print-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .office-copy {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .office-copy:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .customer-copy {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .customer-copy:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        /* Hide print buttons during printing */
        @media print {
            .print-btn, .print-buttons-container {
                display: none !important;
                visibility: hidden !important;
            }

            .section-box:has(.print-buttons-container) {
                display: none !important;
            }

            /* Show signatures only when printing */
            .signature-row {
                display: flex !important;
                justify-content: space-between !important;
                margin-top: 20px !important; 
                padding: 10px !important;
                border-top: 1px solid #ddd !important;
            }
        }
    </style>
</body>
</html>
    </style>
    <script>
        // Wait for DOM to be fully loaded before attaching event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM fully loaded - initializing application");
            
            // Define logout function here as well
            function logout() {
                console.log('Logout initiated from DOMContentLoaded');
                
                const confirmLogout = confirm('Are you sure you want to logout?\n\nAny unsaved changes will be lost.');
                
                if (confirmLogout) {
                    // Clear all session data
                    sessionStorage.clear();
                    localStorage.clear();
                    
                    // Clear cookies
                    document.cookie.split(";").forEach(function(c) { 
                        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                    });
                    
                    // Redirect to login
                    window.location.replace('login.html');
                }
            }
            
            // Make it globally available
            window.logout = logout;
            
            // Also attach to logout button directly if it exists
            const logoutLink = document.querySelector('a[onclick*="logout"]');
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    logout();
                    return false;
                });
            }
            
            // Initialize customer and loan functionality
            initializeCustomerAndLoanFunctions();
            
            // Initialize jewelry items functionality
            initializeJewelryItemsFunctionality();
            
            // Initialize print functionality
            initializePrintFunctionality();
            
            // Initialize image upload functionality
            initializeImageUploadFunctionality();
        });
        
        // Customer and loan related functions
        function initializeCustomerAndLoanFunctions() {
            // Set today's date as default for renewal date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10); // Format: YYYY-MM-DD
            const renewalDateInput = document.getElementById('renewalDate');
            if (renewalDateInput) {
                renewalDateInput.value = formattedDate;
            }
            
            // Format loan amount with currency symbol on blur
            const loanAmountInput = document.getElementById('loanAmount');
            if (loanAmountInput) {
                loanAmountInput.addEventListener('blur', function() {
                    if (this.value) {
                        // Format with 2 decimal places
                        const formattedValue = parseFloat(this.value).toFixed(2);
                        this.value = formattedValue;
                        
                        // Update amount in words
                        updateAmountInWords(this.value);
                    }
                });
                
                // Also update amount in words on input
                loanAmountInput.addEventListener('input', function() {
                    updateAmountInWords(this.value);
                });
            }
            
            // Auto-set renewal date to one year after loan date
            const loanDateInput = document.getElementById('loanDate');
            if (loanDateInput) {
                loanDateInput.addEventListener('change', function() {
                    if (this.value) {
                        const loanDate = new Date(this.value);
                        // Add one year to the loan date
                        const renewalDate = new Date(loanDate);
                        renewalDate.setFullYear(renewalDate.getFullYear() + 1);
                        // Format and set the renewal date
                        const renewalDateFormatted = renewalDate.toISOString().substr(0, 10);
                        document.getElementById('renewalDate').value = renewalDateFormatted;
                    }
                });
            }
        }
        
        // Function to update amount in words with styling
        function updateAmountInWords(amount) {
            console.log("Starting updateAmountInWords function with amount:", amount);
            const amountWordsElement = document.getElementById('loanAmountWords');
            if (amountWordsElement && amount) {
                console.log("Amount in words element found and amount is valid");
                amount = amount.replace(/,/g, ''); // Remove commas if any
                const amountInWords = convertNumberToWords(parseFloat(amount));
                amountWordsElement.textContent = amountInWords;
                amountWordsElement.style.display = 'block';
                amountWordsElement.style.fontWeight = 'bold';
                amountWordsElement.style.color = '#cf0808';
                amountWordsElement.style.fontSize = '16px';
                console.log("Amount in words updated successfully");
            } else {
                console.error("Amount in words element not found or amount is invalid");
                if (amountWordsElement) {
                    amountWordsElement.textContent = '';
                    amountWordsElement.style.display = 'none';
                    console.log("Amount in words element hidden");
                }
                if (amount) {
                    console.error("Invalid amount:", amount);
                }
                if (!amountWordsElement) {
                    console.error("Amount in words element not found");
                }
                if (!amount) {
                    console.error("Amount is invalid");
                }
                console.log("Ending updateAmountInWords function");
            }
        }
        
        // Function to convert number to words
        function convertNumberToWords(num) {
            const units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
            
            function convertWholeNumber(num) {
                if (num === 0) return 'Zero';
                
                let words = '';
                // Handle crores (10,000,000s)
                if (num >= 10000000) {
                    words += convertWholeNumber(Math.floor(num / 10000000)) + ' Crore ';
                    num %= 10000000;
                }       
                // Handle arab (100,000,000s)
                if (num >= 100000000) {
                    words += convertWholeNumber(Math.floor(num / 100000000)) + ' Arab ';
                    num %= 100000000;
                }
                // Handle kharab (1,000,000,000s)
                if (num >= 1000000000) {
                    words += convertWholeNumber(Math.floor(num / 1000000000)) + ' Kharab ';
                    num %= 1000000000;
                }

                // Handle lakhs (100,000s)
                if (num >= 100000) {
                    words += convertWholeNumber(Math.floor(num / 100000)) + ' Lakh ';
                    num %= 100000;
                }
                
                // Handle thousands
                if (num >= 1000) {
                    words += convertWholeNumber(Math.floor(num / 1000)) + ' Thousand ';
                    num %= 1000;
                }
                
                // Handle hundreds
                if (num >= 100) {
                    words += convertWholeNumber(Math.floor(num / 100)) + ' Hundred ';
                    num %= 100;
                }
                
                // Handle tens and units
                if (num > 0) {
                    // Add 'and' if there's a preceding hundreds/thousands/etc.
                    if (words !== '') {
                        words += 'and ';
                    }
                    
                    if (num < 20) {
                        words += units[num];
                    } else {
                        words += tens[Math.floor(num / 10)];
                        if (num % 10 > 0) {
                            words += '-' + units[num % 10];
                        }
                    }
                }
                
                return words;
            }
            
            // Split number into whole and decimal parts
            const wholePart = Math.floor(num);
            const decimalPart = Math.round((num - wholePart) * 100);
            
            let result = convertWholeNumber(wholePart) + ' Rupees';
            
            // Add decimal part if exists
            if (decimalPart > 0) {
                result += ' and ' + convertWholeNumber(decimalPart) + ' Paise';
            }
            
            return result + ' Only';
        }
        
        
        // Jewelry items functionality
        function initializeJewelryItemsFunctionality() {
            // Create the initial jewelry item
            createInitialJewelryItem();
            
            // Add event listener to the Add Jewelry Item button
            const addJewelryItemBtn = document.getElementById('addJewelryItemBtn');
            if (addJewelryItemBtn) {
                addJewelryItemBtn.addEventListener('click', function() {
                    console.log("Add Jewelry Item button clicked");
                    addJewelryItem();
                });
            } else {
                console.error("Add Jewelry Item button not found");
            }
            
            // Calculate initial totals
            calculateTotals();
        }
        
        // Function to create the initial jewelry item
        function createInitialJewelryItem() {
            const jewelryItemsList = document.getElementById('jewelryItemsList');
            if (!jewelryItemsList) {
                console.error("Jewelry items list container not found");
                return;
            }
            
            // Clear any existing items first
            jewelryItemsList.innerHTML = '';
            
            const initialItem = document.createElement('div');
            initialItem.className = 'jewelry-item';
            initialItem.innerHTML = `
                <div class="weight-row">
                    <div class="weight-item">
                        <label for="jewelryCount_0">Jewelry Count:</label>
                        <input type="number" id="jewelryCount_0" class="jewelry-count" min="1" value="1">
                    </div>

                    <div class="weight-item">
                        <label for="jewelryWeight_0">Gross Weight (grams):</label>
                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">
                        <label for
                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">
                        </div>

                    <div class="weight-item">
                        <label for="stoneWeight_0">Stone Weight (grams):</label>
                        <input type="number" id="stoneWeight_0" class="stone-weight" min="0" step="0.1">
                    </div>

                    <div class="weight-item">
                        <label for="netWeight_0">Net Weight (grams):</label>
                        <input type="number" id="netWeight_0" class="net-weight" min="0" step="0.1" readonly>
                    </div>
                </div>

                <div class="jewelry-type-row">
                    <div class="jewelry-selection">
                        <label for="jewelryType_0">Jewelry Type:</label>
                        <select id="jewelryType_0" class="jewelry-type">
                           <option value="">Select Jewelry Type</option>
                            <option value="gold-chain">Gold Chain</option>
                            <option value="gold-ring">Gold Ring</option>
                            <option value="gold-bracelet">Gold Bracelet</option>
                            <option value="gold-necklace">Gold Necklace</option>
                            <option value="gold-earrings">Gold Earrings</option>
                            <option value="gold-bangles">Gold Bangles</option>
                            <option value="gold-pendants">Gold Pendants</option>
                            <option value="gold-ear-studs">Gold Ear Studs</option>
                            <option value="gold-studs">Gold Studs</option>
                            <option value="gold-stone studs ">Gold Stone Studs</option>
                            <option value="gold-mattal">Gold Mattal</option>
                            <option value="gold-baby studs">Gold Baby Studs</option>
                            <option value="gold-lockets">Gold Lockets</option>
                            <option value="gold-anklets">Gold Anklets</option>
                            <option value="gold-manga pich">Gold Manga Pich</option>
                            <option value="gold-talikasu">Gold Talikasu </option>
                            <option value="gold-nose pin">Gold Nose Pin</option>
                            <option value="gold-thali kundu">Gold Thali Kundu</option>
                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>
                            <option value="gold-gold coin">Gold Coin</option>
                            <option value="silver-items">Silver Items</option>
                        </select>
                    </div>

                    <div class="purity-selection">
                        <label for="purityType_0">Purity Type:</label>
                        <select id="purityType_0" class="purity-type">
                            <option value="">Select Purity</option>
                            <option value="24k">24K (99.9%)</option>
                            <option value="22k">22K (91.6%)</option>
                            <option value="18k">18K (75.0%)</option>
                            <option value="14k">14K (58.3%)</option>
                            <option value="10k">10K (41.7%)</option>
                        </select>
                    </div>
                    
                    <div class="item-actions">
                        <button type="button" class="remove-item-btn hide-on-print" data-index="0" style="display: none;">Remove</button>
                    </div>
                </div>
            `;
            
            jewelryItemsList.appendChild(initialItem);
            
            // Initialize net weight calculation for the initial item
            initializeNetWeightCalculation(0);
        }
        
        // Function to add a new jewelry item
        function addJewelryItem() {
            const jewelryItemsList = document.getElementById('jewelryItemsList');
            if (!jewelryItemsList) return;

            const itemCount = jewelryItemsList.querySelectorAll('.jewelry-item').length;
            const newIndex = itemCount;

            const newItem = document.createElement('div');
            newItem.className = 'jewelry-item';
            newItem.innerHTML = `
                <div class="weight-row">
                    <div class="weight-item">
                        <label for="jewelryCount_${newIndex}">Jewelry Count:</label>
                        <input type="number" id="jewelryCount_${newIndex}" class="jewelry-count" min="1" value="1">
                    </div>
                    <div class="weight-item">
                        <label for="jewelryWeight_${newIndex}">Gross Weight (grams):</label>
                        <input type="number" id="jewelryWeight_${newIndex}" class="jewelry-weight" min="0" step="0.1">
                    </div>
                    <div class="weight-item">
                        <label for="stoneWeight_${newIndex}">Stone Weight (grams):</label>
                        <input type="number" id="stoneWeight_${newIndex}" class="stone-weight" min="0" step="0.1">
                    </div>
                    <div class="weight-item">
                        <label for="netWeight_${newIndex}">Net Weight (grams):</label>
                        <input type="number" id="netWeight_${newIndex}" class="net-weight" min="0" step="0.1" readonly>
                    </div>
                </div>
                <div class="jewelry-type-row">
                    <div class="jewelry-selection">
                        <label for="jewelryType_${newIndex}">Jewelry Type:</label>
                        <select id="jewelryType_${newIndex}" class="jewelry-type">
                            <option value="">Select Jewelry Type</option>
                            <option value="gold-chain">Gold Chain</option>
                            <option value="gold-ring">Gold Ring</option>
                            <option value="gold-bracelet">Gold Bracelet</option>
                            <option value="gold-necklace">Gold Necklace</option>
                            <option value="gold-earrings">Gold Earrings</option>
                            <option value="gold-bangles">Gold Bangles</option>
                            <option value="gold-pendants">Gold Pendants</option>
                            <option value="gold-ear-studs">Gold Ear Studs</option>
                            <option value="gold-studs">Gold Studs</option>
                            <option value="gold-stone studs ">Gold Stone Studs</option>
                            <option value="gold-mattal">Gold Mattal</option>
                            <option value="gold-baby studs">Gold Baby Studs</option>
                            <option value="gold-lockets">Gold Lockets</option>
                            <option value="gold-anklets">Gold Anklets</option>
                            <option value="gold-manga pich">Gold Manga Pich</option>
                            <option value="gold-talikasu">Gold Talikasu </option>
                            <option value="gold-nose pin">Gold Nose Pin</option>
                            <option value="gold-thali kundu">Gold Thali Kundu</option>
                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>
                            <option value="gold-gold coin">Gold Coin</option>
                            <option value="silver-items">Silver Items</option>
                        </select>
                    </div>
                    <div class="purity-selection">
                        <label for="purityType_${newIndex}">Purity Type:</label>
                        <select id="purityType_${newIndex}" class="purity-type">
                            <option value="">Select Purity</option>
                            <option value="24k">24K (99.9%)</option>
                            <option value="22k">22K (91.6%)</option>
                            <option value="18k">18K (75.0%)</option>
                            <option value="14k">14K (58.3%)</option>
                            <option value="10k">10K (41.7%)</option>
                        </select>
                    </div>
                    <div class="item-actions">
                        <button type="button" class="remove-item-btn hide-on-print" data-index="${newIndex}">Remove</button>
                    </div>
                </div>
            `;

            jewelryItemsList.appendChild(newItem);

   // Hide remove button only for the first item
            const removeBtn = newItem.querySelector('.remove-item-btn');
            if (removeBtn) {
                console.log("Remove button found:", removeBtn);
                
                if (newIndex === 0) {
                    removeBtn.classList.add('hide-on-print');
                    removeBtn.style.display = 'none';
                } else {
                    removeBtn.style.display = 'inline-block';
                }
                removeBtn.addEventListener('click', function() {
                    removeJewelryItem(newIndex);
                });
            }

            // Attach event listeners for calculation
            newItem.querySelector('.jewelry-count').addEventListener('input', calculateTotals);
            newItem.querySelector('.jewelry-weight').addEventListener('input', calculateTotals);
            newItem.querySelector('.stone-weight').addEventListener('input', calculateTotals);

            // Initialize net weight calculation for the new item
            initializeNetWeightCalculation(newIndex);

            // Show the remove button on the first item if we have more than one item
            if (jewelryItemsList.querySelectorAll('.jewelry-item').length > 0) {
                const firstItemRemoveBtn = jewelryItemsList.querySelector('.jewelry-item:first-child .remove-item-btn');
                if (firstItemRemoveBtn) {
                    firstItemRemoveBtn.style.display = 'inline-block';
                }
            }

            calculateTotals();
        }
        
        // Function to remove a jewelry item
        function removeJewelryItem(index) {
            console.log(`Removing jewelry item with index: ${index}`);
            
            const jewelryItemsList = document.getElementById('jewelryItemsList');
            if (!jewelryItemsList) {
                console.error("Jewelry items list not found");
                return;
            }
            
            const items = jewelryItemsList.querySelectorAll('.jewelry-item');
            if (!items || items.length == 0) {
                console.error("No jewelry items found");
                return;
            }
            
            // Don't remove if it's the last item
            if (items.length <= 0) {
                console.log("Cannot remove the last item");
                return;
            }
            
            console.log(`Total items before removal: ${items.length}`);
            console.log(`Attempting to remove item with index: ${index}`);
            // Check if the index is valid
            if (index < 0 || index >= items.length) {
                console.error(`Invalid index: ${index}. Cannot remove item.`);
                return;
            }
            console.log(`Valid index: ${index}. Proceeding with removal.`);
            // Log the current items for debugging
            items.forEach((item, i) => {
                console.log(`Item ${i}:`, item);
            });
            
            console.log(`Items before removal: ${items.length}`);
            // Log the remove button for debugging
            const removeBtn = document.querySelector(`.remove-item-btn[data-index="${index}"]`);
            if (removeBtn) {
                console.log("Remove button found:", removeBtn);
            } else {
                console.error(`Remove button with index ${index} not found`);
            }
            // Log the jewelry items list for debugging
            console.log("Jewelry items list:", jewelryItemsList);
            // Log the items in the jewelry items list
            console.log("Items in jewelry items list:", items);
                console.log(`Items in jewelry items list: ${items.length}`);
            // Log the jewelry items list container
            console.log("Jewelry items list container:", jewelryItemsList);
            // Log the current state of the jewelry items list
            console.log("Current jewelry items list state:", jewelryItemsList.innerHTML);
            // Find the item with the matching index and remove it
            for (let i = 0; i < items.length; i++) {
                const removeBtn = items[i].querySelector('.remove-item-btn');
                if (removeBtn && removeBtn.getAttribute('data-index') == index) {
                    try {
                        items[i].remove();
                        console.log(`Item with index ${index} removed`);
                    } catch (error) {
                        console.error(`Error removing item with index ${index}: ${error}`);
                    }
                    break;
                } else {
                    console.error(`Remove button with index ${index} not found`);
                }
            }
            
            // Hide the remove button on the first item if we only have one item left
            if (jewelryItemsList.querySelectorAll('.jewelry-item').length = 0) {
                const firstItemRemoveBtn = document.querySelector('.jewelry-item:second-child .remove-item-btn');
                if (firstItemRemoveBtn) {
                    try {
                        firstItemRemoveBtn.style.display = 'none';
                        firstItemRemoveBtn.classList.add('hide-on-print');
                        console.log("First item remove button hidden");
                    } catch (error) {
                        console.error(`Error hiding first item remove button: ${error}`);
                    }
                } else {
                    console.error("First item remove button not found");
                }
            }
            
            // Calculate totals after removing an item
            try {
                calculateTotals();
            } catch (error) {
                console.error(`Error calculating totals after removing item with index ${index}: ${error}`);
            }
        }
        
        // Function to initialize net weight calculation
        function initializeNetWeightCalculation(index) {
            const jewelryWeightInput = document.getElementById(`jewelryWeight_${index}`);
            const stoneWeightInput = document.getElementById(`stoneWeight_${index}`);
            const netWeightInput = document.getElementById(`netWeight_${index}`);
            
            if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {
                console.error(`Could not find weight inputs for index ${index}`);
                return;
            }
            
            // Function to calculate net weight
            function calculateNetWeight() {
                const jewelryWeight = parseFloat(jewelryWeightInput.value) || 0;
                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                const netWeight = jewelryWeight - stoneWeight;
                netWeightInput.value = netWeight > 0 ? netWeight.toFixed(1) : 0;
                
                // Update totals whenever an individual weight changes
                calculateTotals();
            }
            
            // Add event listeners for weight inputs
            jewelryWeightInput.addEventListener('input', calculateNetWeight);
            stoneWeightInput.addEventListener('input', calculateNetWeight);
        }
        
        // Function to calculate totals from all jewelry items
        function calculateTotals() {
            console.log("Calculating totals");
            let totalJewelryCount = 0;
            let totalJewelryWeight = 0;
            let totalStoneWeight = 0;
            let totalNetWeight = 0;
            
            // Get all jewelry items
            const jewelryItems = document.querySelectorAll('.jewelry-item');
            
             // Calculate totals
    jewelryItems.forEach(item => {
        const jewelryCount = parseFloat(item.querySelector('.jewelry-count')?.value) || 0;
        const jewelryWeight = parseFloat(item.querySelector('.jewelry-weight')?.value) || 0;
        const stoneWeight = parseFloat(item.querySelector('.stone-weight')?.value) || 0;
        const netWeight = parseFloat(item.querySelector('.net-weight')?.value) || 0;

        totalJewelryCount += jewelryCount;
        totalJewelryWeight += jewelryWeight;
        totalStoneWeight += stoneWeight;
        totalNetWeight += netWeight;
    });
            
          // Update total fields
    document.getElementById('totalJewelryCount').value = totalJewelryCount;
    document.getElementById('totalJewelryWeight').value = totalJewelryWeight.toFixed(1);
    document.getElementById('totalStoneWeight').value = totalStoneWeight.toFixed(1);
    document.getElementById('totalNetWeight').value = totalNetWeight.toFixed(1);

    console.log(`Totals calculated - Count: ${totalJewelryCount}, Jewelry: ${totalJewelryWeight}, Stone: ${totalStoneWeight}, Net: ${totalNetWeight}`);
}
        
        // Initialize print functionality
        function initializePrint() {
            // Replace all print buttons with new ones
            const printBtns = document.querySelectorAll('.print-btn');
            printBtns.forEach(btn => {
                const newBtn = document.createElement('button');
            
                
                if (btn.parentNode) {
                    btn.parentNode.replaceChild(newBtn, btn);
                }
            });
            
            // Add event listener to the new print button
            const newPrintBtn = document.querySelector('.print-btn');
            if (newPrintBtn) {
                newPrintBtn.addEventListener('click', singlePrintFunction);
            }
        }
        
        // Single print function for both office and customer copies
        function singlePrintFunction(event) {
            event.preventDefault();
            printDocument('office');
        }
             
        // Print functionality
        function initializePrintFunctionality() {
            // Remove any inline onclick attributes from the HTML
            document.querySelectorAll('[onclick*="print"]').forEach(el => {
                el.removeAttribute('onclick');
            });
            
            // Add event listeners to both print buttons
            const officeCopyBtn = document.querySelector('.office-copy');
            const customerCopyBtn = document.querySelector('.customer-copy');
            
            if (officeCopyBtn) {
                officeCopyBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    printDocument('office');
                });
            }
            
            if (customerCopyBtn) {
                customerCopyBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    printDocument('customer');
                });
            }
        }
        
        // Print function for both office and customer copies
        let isPrinting = false; // Flag to prevent multiple calls
        function printDocument(copyType) {
            if (isPrinting) return;
            isPrinting = true;

            try {
                // Format loan amount for printing
                const loanAmount = document.getElementById('loanAmount').value;
                if (loanAmount) {
                    try {
                        const amountInWords = convertNumberToWords(parseFloat(loanAmount));
                        document.getElementById('loanAmountWords').textContent = amountInWords;
                    } catch (e) {
                        console.error("Error converting amount to words:", e);
                        document.getElementById('loanAmountWords').textContent = "Amount in words not available";
                    }
                }

                // Add copy type class to body for print
                document.body.classList.add('a4-print');
                if (copyType === 'office') {
                    document.body.classList.add('office-copy-print');
                } else if (copyType === 'customer') {
                    document.body.classList.add('customer-copy-print');
                }

                // Trigger the print dialog after a short delay
                setTimeout(function() {
                    window.print();

                    // Reset after printing
                    setTimeout(function() {
                        document.body.classList.remove('a4-print', 'office-copy-print', 'customer-copy-print');
                        isPrinting = false;

                        // Clean up any temporary elements
                        document.querySelectorAll('.temp-print-element').forEach(el => el.remove());
                    }, 1000);
                }, 100);
            } catch (error) {
                console.error("Error in print function:", error);
                alert("There was an error with the print function. Please try again.");
                isPrinting = false;
            }
            return false;
        }
    </script>
    
    <style>
        /* Images container and boxes styling */
        .images-container {
            color: #f70000;
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            width: 100%;
        }
        
        .image-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
            background-color: #f9f9f9;
            color: #e70606;
        }
        
        .customer-box {
            flex-basis: 20%;
            max-width: 20%;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .jewelry-box {
            flex-basis: 30%;
        }
        
        .box-header {
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            margin-top: 10px;
            color: #cf0808; /* Dark red color for headers */
        }
        .box-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 10px;
            text-align: center;
            position: relative;
            color: #fc0000;
        }
        .image-preview-container, .jewelry-placeholder {
            margin-top: 10px;
            text-align: center;
            position: relative;
            width: 120px;
            height: 120px;
        }
        #customerPhotoPreview, .jewelry-preview-img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            background-color: #fff;
        }
        /* Jewelry images container */
        #jewelryPreviewContainer {
            width: 100%;
            max-width: 100%;
            height: auto;
            color: #000;
            display: flex;
            flex-wrap: wrap;
            gap: 50px;
            margin-top: 50px;
            justify-content: center;
        }
        /* Print styles for the image boxes */
        @media print {
            .image-box {
                flex:  50%;
                max-width: 50%;
                border: none !important;
                padding: 0 !important;
                background: none !important;
                box-shadow: none !important;
            }
            .customer-box, .jewelry-box {
                   flex:  50%;
                max-width: 50%;
                border: none !important;
                padding: 0 !important;
                background: none !important;
                box-shadow: none !important;
            }
            }
            .image-preview-container, .jewelry-placeholder {
                width: 12px;
                height: 12px;
                margin-top: 0px;
                text-align: center;
                position: relative;
                border: none !important;
                padding: 0 !important;
                background: none !important;
                box-shadow: none !important;
            }
                     /* Make customer and jewelry preview images fit properly */
                    
            #customerPhotoPreview,
            .jewelry-preview-img {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                justify-content: center;    
                width: 100%;
                max-width: 100%;
                flex-direction: column;
                align-items: center;
            }
            #jewelryPreviewContainer {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                justify-content: center;    
                width: 100%;
                max-width: 100%;
                flex-direction: column;
                align-items: center;

            }
            .images-container {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                justify-content: center;    
                width: 50%;
                max-width: 0%;
                flex-direction: column;
                align-items: center;
                
            }
            
            .image-box {
              display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                justify-content: center;    
                width: 100%;
                max-width: 100%;
                flex-direction: column;
                align-items: center;

            }
        }
    </style>
    <script>
        /**
         * Automatically calculates the net weight whenever either the total weight or stone weight inputs change.
         * This function is triggered whenever either the total weight or stone weight inputs change.
         *
         * @param {Event} event - The input event that triggered this function.
         */
        function calculateNetWeight(event) {
            console.log("Calculating net weight");

            try {
                // Get the total weight, stone weight, and net weight input elements
                const jewelryWeightInput = document.getElementById('jewelryWeight');
                const stoneWeightInput = document.getElementById('stoneWeight');
                const netWeightInput = document.getElementById('netWeight');

                // Check if any of the inputs are missing
                if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {
                    throw new Error("Could not find weight inputs");
                }

                // Parse the total weight and stone weight inputs and calculate the net weight
                const totalWeight = parseFloat(jewelryWeightInput.value) || 0;
                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                console.log(`Total weight: ${totalWeight}`);
                console.log(`Stone weight: ${stoneWeight}`);
                const netWeight = totalWeight - stoneWeight;

                // If the net weight is positive, update the net weight input field with the result
                if (netWeight >= 0) {
                    netWeightInput.value = netWeight.toFixed(2);
                    console.log(`Net weight: ${netWeight.toFixed(2)}`);
                } else {
                    console.error("Net weight is negative");
                }
            } catch (error) {
                console.error(error);
            }
        }
        // Add event listeners for weight inputs
        
        document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);
        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);
        // Function to update amount in words   
        function calculateNetWeight() {
            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0
        }
    </script>
    <script>
        // Calculate net weight automatically
        function calculateNetWeight() {
            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0;
            const stoneWeight = parseFloat(document.getElementById('stoneWeight').value) || 0;
            const netWeight = totalWeight - stoneWeight;

            if (netWeight >= 0) {
                document.getElementById('netWeight').value = netWeight.toFixed(2);
            }
        }
        // Add event listeners for weight inputs
        
        document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);
        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);
        // Function to update amount in words   
        function calculateNetWeight() {
            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0
        }
    </script>
    <style>
        /* General styles */
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #070707;
            overflow-x: hidden; /* Prevent horizontal scroll */
            position: relative; /* For absolute positioning of child elements */
            font-size: 16px; /* Base font size */
            scroll-behavior: smooth; /* Smooth scrolling */
        }

        html {
            height: 10%;
            font-size: 16px; /* Base font size */
            scroll-behavior: smooth; /* Smooth scrolling */
            background-color: #f4f4f4; /* Light background color */
            color: #2505b4; /* Default text color */
            font-family: Arial, sans-serif;
            overflow-x: hidden; /* Prevent horizontal scroll */
            position: relative; /* For absolute positioning of child elements */
            padding: 0;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: top 20px;
            padding  20px;
        }
        
        .container {
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);  
            background: rgb(247, 245, 245);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 100vh;
            justify-content: space-between;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);            
        }
        
        h1, h2 {
            text-align: center;
            color: #cf0808;   
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 0;
            line-height: 1.2;
            font-family: 'Arial', sans-serif;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            
        }
        
        .section-box {
            margin-bottom: 10px;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
               }
        
        .form-group {
            margin-bottom: 8px;
        }
        
        .form-group label {
            margin-bottom: 2px;
            font-size: 13px;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }

                .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 40px;
            margin-bottom: 10px;
        }
        
        .image-item {
            width: 220px;
            min-width: 200px;
            max-width: 240px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 15px 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .image-item label {
            font-weight: bold;
            margin-bottom: 10px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }
        
        .file-info {
            font-size: 12px;
            color: #888;
            margin-bottom: 6px;
            width: 75%;
            text-align: center;
            border-radius: 5px;
            background: #f9f9f9;
            padding: 4px 0;
        }
        
        .image-preview-box {
            width: 100px;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fafafa;
            box-shadow: 0 2px 4px rgba(2, 2, 2, 0.87);
            padding: 5px;
            box-sizing: border-box;
            overflow: hidden;
        }

        #customerPhotoPreview,
        .jewelry-preview-img {
            width: auto !important;
            height: auto !important;
            object-fit: contain;
            border-radius: 6px;
            background: #fff;
            display: block;
        }

                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #f7f7f7;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #000000;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4 portrait centered;
        margin: 5mm;
    }

    /* Basic print setup */
    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    /* Container adjustments */
    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 5mm !important;
        min-height: auto !important;
    }

  
    
    .header-logo img {
        width: 20mm !important;
        height: 20mm !important;
    }

    /* Section spacing */
    .section-box {
        margin-bottom: 2mm !important;
        padding: 2mm !important;
    }

    /* Form elements */
    .form-group {
        margin-bottom: 1mm !important;
    }

    .form-group label {
        margin-bottom: 0.5mm !important;
    }

    /* Image sizes */
    .image-preview-box {
        width: 25mm !important;
        height: 25mm !important;
        border: 1px solid #333 !important;
        margin: 2mm auto !important;
    }
    
    .image-preview-box img,
    #customerPhotoPreview,
    .jewelry-preview-img {
        width: 100% !important;
        height: 100% !important;
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
    }

    /* Weight row adjustments */
    .weight-row {
        gap: 1mm !important;
    }

    .weight-item {
        padding: 1mm !important;
    }


    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
</style>
</head>
    <style>
        /* Fixed image sizing - make both photos fill containers equally */
        .simple-image-container {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }

        .simple-image-item {
            flex: 1;
            text-align: center;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
        }

        .simple-image-item label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .image-preview-area {
            width: 150px;
            height: 150px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            border: 1px solid #ccc;
            background: #f9f9f9;
        }

        #customerPhotoPreview, #jewelryPhotoPreview {
            width: 148px;
            height: 148px;
            object-fit: cover;
            border-radius: 4px;
        }

        /* Print styles - make both images exactly the same size */
        @media print {
            input[type="file"] {
                display: none !important;
            }
            
            .simple-image-container {
                display: flex !important;
                gap: 20px !important;
                margin: 10px 0 !important;
                justify-content: space-between !important;
            }
            
            .simple-image-item {
                flex: 1 !important;
                border: 2px solid #000 !important;
                padding: 10px !important;
                border-radius: 8px !important;
                background: #fff !important;
                max-width: 45% !important;
                min-height: 140px !important;
                width: 45% !important;
            }
            
            .simple-image-item label {
                font-weight: bold !important;
                font-size: 12px !important;
                margin-bottom: 8px !important;
                display: block !important;
                text-align: center !important;
            }
            
            .image-preview-area {
                width: 120px !important;
                height: 120px !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                border: 1px solid #333 !important;
                background: #fff !important;
                margin: 5px auto !important;
                border-radius: 4px !important;
            }
            
            /* Force both images to be exactly the same size */
            #customerPhotoPreview, #jewelryPhotoPreview {
                width: 115px !important;
                height: 115px !important;
                max-width: 115px !important;
                max-height: 115px !important;
                min-width: 115px !important;
                min-height: 115px !important;
                object-fit: cover !important;
                display: block !important;
                border-radius: 3px !important;
                border: none !important;
            }
            
            /* Hide file inputs and camera buttons */
            input[type="file"], .camera-btn, .file-info {
                display: none !important;
            }
            
            /* Ensure images are visible and same size */
            .simple-image-item img {
                display: block !important;
                visibility: visible !important;
                width: 115px !important;
                height: 115px !important;
            }
            
            /* Specific targeting for jewelry images */
            .jewelry-preview-img {
                width: 115px !important;
                height: 115px !important;
                max-width: 115px !important;
                max-height: 115px !important;
                min-width: 115px !important;
                min-height: 115px !important;
                object-fit: cover !important;
            }
            .jewelry-placeholder {
                width: 115px !important;
                height: 115px !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                border: 1px solid #ddd !important;
                background: #f9f9f9 !important;
                border-radius: 4px !important;
            }
        }

</head>
</html>
    <script>
        // Logout function
        function logout() {
            if (confirm("Are you sure you want to logout?")) {
                window.location.href = "/logout";
            }
            return false;
        }

    </script>
    </script>
</body>
</html>
