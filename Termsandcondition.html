<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms and Conditions - Amman Gold Finance</title>
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 30px;
        }

        .sidebar-header img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            color: #ecf0f1;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #3498db;
        }

        .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: white;
            border-left-color: #3498db;
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 30px;
            background: white;
        }

        .content-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .content-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }

        .terms-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            line-height: 1.8;
        }

        .terms-content p {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: justify;
        }

        .signature-section {
            margin-top: 50px;
            text-align: right;
            padding: 20px;
            border-top: 2px solid #ecf0f1;
        }

        .signature-section p {
            font-weight: bold;
            font-size: 18px;
            color: #2c3e50;
        }

        .print-btn {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-top: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
            
            .main-content {
                margin-left: 200px;
                padding: 20px;
            }
        }

        @media (max-width: 600px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .nav-menu {
                display: flex;
                overflow-x: auto;
            }
            
            .nav-item {
                margin-bottom: 0;
                margin-right: 5px;
            }
        }

        /* Print Styles */
        @media print {
            .sidebar {
                display: none !important;
            }
            
            .main-content {
                margin-left: 0 !important;
                padding: 0 !important;
            }
            
            .print-btn {
                display: none !important;
            }
            
            .content-header {
                background: none !important;
                color: black !important;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="20250608_130850.png" alt="Logo">
            <h3>AMMAN GOLD FINANCE</h3>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="main.html" class="nav-link">
                    <span class="nav-icon">💰</span>
                    Loan Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="interestcreation.html" class="nav-link">
                    <span class="nav-icon">📊</span>
                    Interest Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="Termsandcondition.html" class="nav-link active">
                    <span class="nav-icon">📋</span>
                    Terms And Condition
                </a>
            </li>
            <li class="nav-item">
                <a href="#" onclick="logout(); return false;" class="nav-link">
                    <span class="nav-icon">🚪</span>
                    Logout
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <h1>விதிமுறைகள் மற்றும் நிபந்தனைகள்</h1>
            <h1>Terms and Conditions</h1>
        </div>

        <div class="terms-content">
            <p>
                1. நீங்கள் ஈடாக வைத்திருக்கும் பொருளுக்கு கூடுதல் கடன் மதிப்பீட்டை வைத்து உங்கள் வட்டி நிர்ணயிக்கப்படுகிறது.
            </p>
            
            <p>
                2. அடகு வைத்த தேதியிலிருந்து ஒரு வருடத்திற்குள் நகைகளை திருப்பவோ அல்லது மாற்றி புதுப்பித்து கொள்ளவோ தவறும் பட்சத்தில் அடுத்து வரும் நாட்களில் உங்கள் நகைகளை பகிரங்க ஏலத்தில் விடப்படும்.
            </p>
            
            <p>
                3. அடகு கடன் பெற்றவர்களுக்கு அனுப்பபடும் தபால்கள், இதர செலவுகள் அனைத்தும் தங்களின் கடன் கணக்கிலேயே சேரும்.
            </p>
            
            <p>
                4. நான் கடனாக வாங்கிய தொகை நேர்மையான தேவைக்கு மட்டுமே பயன்படுத்துவேன் அல்லாமல் நேரடியாகவோ, மறைமுகமாகவோ சட்டவிரோத செயல்களுக்கு இந்த பணம் பயன்படுத்தமாட்டேன் என் நிச்சயமாக உறுதி கூறுகிறேன்.
            </p>
            
            <p>
                5. நான் கடன் பெற்ற இந்த நாள் வரை என் மீது எந்த ஒரு திவாலா நோட்டீசும், கடன் தீர்க்க வகையற்றவர் என்ற புகாரும், இது தொடர்பாக எந்த ஒரு நீதிமன்ற நடவடிக்கையும் இது நாள் வரை என் மீது இல்லை என்று உறுதி கூறுகிறேன்.
            </p>
            
            <p>
                6. நகைக்கடன் சம்பந்தமாக அதன் விதிமுறைகளைக் கூட்டவோ, மாற்றவோ, நீக்கவோ நிறுவனத்திற்கு முழு உரிமை உண்டு.
            </p>
            
            <p>
                7. அலுவலக நாட்களில் நகையை திருப்ப திங்கள் முதல் வெள்ளி வரை மதியம் 1.00 மணிக்குள் பணம் செலுத்தி பிற்பகல் 3.00 மணிக்கு மேல் அடகு நகையை திரும்ப பெற்றுக் கொள்ள சம்மதிக்கிறேன். சனி, ஞாயிறு மற்றும் அரசு, உள்ளூர் விடுமுறை நாட்களிலும் நகை திருப்பித்தர இயலாது.
            </p>
            
            <p>
                8. தொகைக்கு நிகரான தங்க நகைகள் அடகு வைத்த தொகை வாங்குவதிலும் அல்லது எழுத்து வடிவில் ஈடு செய்யவும் நிறுவனத்திற்கு அதிகாரப்படுத்துகிறேன்.
            </p>
            
            <p>
                9. நான் அடமானமாக வைக்க தங்கள் நிறுவனத்திற்கு கொண்டு வந்து இருக்கும் நகைகள் அனைத்தும் என்னுடைய சொந்த நகைகள் தான் என்பதை உறுதியுடன் தெரிவித்துக் கொள்கிறேன்.
            </p>
            
            <p>
                10. தங்களது முகவரி மற்றும் அலைபேசி எதுவும் மாற்றம் இருந்தால் அதை உடனடியாக நிர்வாகத்திற்கு தெரியப்படுத்த வேண்டும் தவறும் பட்சத்தில் நிர்வாகம் பொறுப்பேற்காது.
            </p>

            <div class="signature-section">
                <p>பணம் பெறுவோரின் கையொப்பம்</p>
            </div>

            <button onclick="window.print()" class="print-btn">
                <span>🖨️</span>
                Print Document
            </button>
        </div>
    </div>

    <script>
        // Check authentication
        document.addEventListener('DOMContentLoaded', function() {
            if (!isAuthenticated()) {
                window.location.href = 'login.html';
                return;
            }
        });

        function isAuthenticated() {
            const isLoggedIn = sessionStorage.getItem('isLoggedIn');
            const loginTime = sessionStorage.getItem('loginTime');
            
            if (isLoggedIn !== 'true' || !loginTime) {
                return false;
            }
            
            const loginDate = new Date(loginTime);
            const now = new Date();
            const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
            
            if (hoursDiff >= 24) {
                sessionStorage.clear();
                return false;
            }
            
            return true;
        }

        // Direct logout function
        function logout() {
            const confirmLogout = confirm('Are you sure you want to logout?\n\nAny unsaved changes will be lost.');
            
            if (confirmLogout) {
                // Clear all session data
                sessionStorage.clear();
                localStorage.clear();
                
                // Clear cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                // Redirect to login
                window.location.replace('login.html');
            }
        }

        window.logout = logout;
    </script>
</body>
</html>
