<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance - Login</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <style>
        /* Login Page Styles */
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
        }

        .login-header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }

        .login-header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            margin-top: 15px;
            padding: 10px;
            background: #fdf2f2;
            border: 1px solid #fecaca;
            border-radius: 5px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            margin-top: 15px;
            padding: 10px;
            background: #f0f9f4;
            border: 1px solid #a7f3d0;
            border-radius: 5px;
            display: none;
        }

        .forgot-password {
            margin-top: 20px;
            text-align: center;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="20250608_130850.png" alt="Gold Finance Logo">
            <h1>AMMAN GOLD FINANCE</h1>
            <p>Secure Login Portal</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="userId">User ID:</label>
                <input type="text" id="userId" name="userId" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>

            <button type="submit" id="loginBtn" class="login-btn">
                <span id="loginText">Login</span>
                <span id="loginSpinner" class="spinner hidden"></span>
            </button>

            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>
        </form>

        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
        </div>
    </div>

    <script>
        // User credentials (in production, this should be server-side)
        const validUsers = {
            'admin': 'admin123',
            'manager': 'manager123',
            'cashier': 'cashier123',
            'user': 'user123',
            'test': 'test123'
            // Add more users as needed
                
        };

        // DOM elements
        const loginForm = document.getElementById('loginForm');
        const userIdInput = document.getElementById('userId');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // Login form submission
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        // Handle login process
        function handleLogin() {
            const userId = userIdInput.value.trim();
            const password = passwordInput.value.trim();

            // Clear previous messages
            hideMessages();

            // Validation
            if (!userId || !password) {
                showError('Please enter both User ID and Password');
                return;
            }

            // Show loading state
            showLoading(true);

            // Simulate API call delay
            setTimeout(() => {
                if (validateCredentials(userId, password)) {
                    handleSuccessfulLogin(userId);
                } else {
                    handleFailedLogin();
                }
            }, 1000);
        }

        // Validate user credentials
        function validateCredentials(userId, password) {
            return validUsers[userId] && validUsers[userId] === password;
        }

        // Handle successful login
        function handleSuccessfulLogin(userId) {
            showLoading(false);
            showSuccess('Login successful! Redirecting...');
            
            // Store user session
            sessionStorage.setItem('isLoggedIn', 'true');
            sessionStorage.setItem('userId', userId);
            sessionStorage.setItem('loginTime', new Date().toISOString());

            // Redirect to main application after 1.5 seconds
            setTimeout(() => {
                window.location.href = 'main.html'; // Your main loan page
            }, 1500);
        }

        // Handle failed login
        function handleFailedLogin() {
            showLoading(false);
            showError('Invalid User ID or Password. Please try again.');
            
            // Clear password field
            passwordInput.value = '';
            passwordInput.focus();
        }

        // Show loading state
        function showLoading(isLoading) {
            if (isLoading) {
                loginBtn.disabled = true;
                loginText.textContent = 'Logging in...';
                loginSpinner.classList.remove('hidden');
            } else {
                loginBtn.disabled = false;
                loginText.textContent = 'Login';
                loginSpinner.classList.add('hidden');
            }
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        // Show success message
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        // Hide all messages
        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // Forgot password functionality
        function showForgotPassword() {
            alert('Please contact your administrator to reset your password.');
            return false;
            
        }

        // Auto-focus on User ID field
        window.addEventListener('load', function() {
            userIdInput.focus();
        });

        // Enter key handling
        userIdInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                passwordInput.focus();
            }
        });

        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });

        // Check if user is already logged in
        window.addEventListener('load', function() {
            if (sessionStorage.getItem('isLoggedIn') === 'true') {
                // Check if session is still valid (24 hours)
                const loginTime = new Date(sessionStorage.getItem('loginTime'));
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                
                if (hoursDiff < 24) {
                    window.location.href = 'main.html';
                } else {
                    // Session expired, clear storage
                    sessionStorage.clear();
                }
            }
        });
    </script>
</body>
</html>
