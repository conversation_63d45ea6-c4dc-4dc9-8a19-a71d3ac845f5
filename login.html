<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance - Login</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <style>
        /* Login Page Styles with Enhanced Animations */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        /* Animated background gradient */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating particles background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
            background-size: 200px 200px, 300px 300px, 150px 150px;
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
            transform: translateY(50px);
            opacity: 0;
            animation: slideInUp 0.8s ease-out forwards;
            position: relative;
            z-index: 1;
        }

        @keyframes slideInUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .login-header {
            margin-bottom: 30px;
            animation: fadeInDown 0.8s ease-out 0.2s both;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            from {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }
            to {
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            }
        }

        .login-header img:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .login-header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShine 3s ease-in-out infinite;
        }

        @keyframes textShine {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .login-header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.8);
            position: relative;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .form-group input:focus + label,
        .form-group input:not(:placeholder-shown) + label {
            color: #667eea;
            transform: translateY(-2px);
        }

        /* Input field hover effect */
        .form-group input:hover {
            border-color: #a5b4fc;
            background: rgba(255, 255, 255, 0.9);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 200% 200%;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            background-position: right center;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .login-btn:disabled::before {
            display: none;
        }

        .error-message {
            color: #e74c3c;
            margin-top: 15px;
            padding: 12px;
            background: linear-gradient(135deg, #fdf2f2, #fef7f7);
            border: 1px solid #fecaca;
            border-radius: 10px;
            display: none;
            animation: shakeIn 0.5s ease-out;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.1);
        }

        @keyframes shakeIn {
            0% { transform: translateX(-10px); opacity: 0; }
            25% { transform: translateX(10px); }
            50% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
            100% { transform: translateX(0); opacity: 1; }
        }

        .success-message {
            color: #27ae60;
            margin-top: 15px;
            padding: 12px;
            background: linear-gradient(135deg, #f0f9f4, #f7fdf9);
            border: 1px solid #a7f3d0;
            border-radius: 10px;
            display: none;
            animation: bounceIn 0.6s ease-out;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.1);
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .forgot-password {
            margin-top: 20px;
            text-align: center;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            position: relative;
        }

        .forgot-password a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transition: all 0.3s ease;
        }

        .forgot-password a:hover {
            color: #764ba2;
            transform: translateY(-1px);
        }

        .forgot-password a:hover::after {
            width: 100%;
            left: 0;
        }

        /* Enhanced Loading spinner */
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #ffffff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Responsive design improvements */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
                max-width: none;
            }

            .login-header h1 {
                font-size: 20px;
            }

            .form-group input {
                padding: 14px 15px;
                font-size: 16px;
            }

            .login-btn {
                padding: 16px;
                font-size: 16px;
            }
        }

        /* Additional micro-interactions */
        .form-group {
            position: relative;
        }

        .form-group input:focus ~ .input-highlight {
            transform: scaleX(1);
        }

        .input-highlight {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        /* Shake animation for errors */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        /* Focus state for form groups */
        .form-group.focused {
            transform: translateY(-2px);
        }

        /* Additional polish animations */
        .login-container:hover {
            transform: translateY(-2px);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="20250608_130850.png" alt="Gold Finance Logo">
            <h1>AMMAN GOLD FINANCE</h1>
            <p>Secure Login Portal</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="userId">User ID:</label>
                <input type="text" id="userId" name="userId" required autocomplete="username" placeholder="Enter your user ID">
                <div class="input-highlight"></div>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password" placeholder="Enter your password">
                <div class="input-highlight"></div>
            </div>

            <button type="submit" id="loginBtn" class="login-btn">
                <span id="loginText b">Login</span>
                <span id="loginSpinner" class="spinner hidden"></span>
            </button>

            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>
        </form>

        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
        </div>
    </div>

    <script>
        // User credentials (in production, this should be server-side)
        const validUsers = {
            'admin': 'admin123',
            'manager': 'manager123',
            'cashier': 'cashier123',
            'user': 'user123',
            'test': 'test123'
            // Add more users as needed
                
        };

        // DOM elements
        const loginForm = document.getElementById('loginForm');
        const userIdInput = document.getElementById('userId');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // Login form submission
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        // Handle login process
        function handleLogin() {
            const userId = userIdInput.value.trim();
            const password = passwordInput.value.trim();

            // Clear previous messages
            hideMessages();

            // Validation
            if (!userId || !password) {
                showError('Please enter both User ID and Password');
                return;
            }

            // Show loading state
            showLoading(true);

            // Simulate API call delay
            setTimeout(() => {
                if (validateCredentials(userId, password)) {
                    handleSuccessfulLogin(userId);
                } else {
                    handleFailedLogin();
                }
            }, 1000);
        }

        // Validate user credentials
        function validateCredentials(userId, password) {
            return validUsers[userId] && validUsers[userId] === password;
        }

        // Handle successful login
        function handleSuccessfulLogin(userId) {
            showLoading(false);
            showSuccess('Login successful! Redirecting...');
            
            // Store user session
            sessionStorage.setItem('isLoggedIn', 'true');
            sessionStorage.setItem('userId', userId);
            sessionStorage.setItem('loginTime', new Date().toISOString());

            // Redirect to main application after 1.5 seconds
            setTimeout(() => {
                window.location.href = 'main.html'; // Your main loan page
            }, 1500);
        }

        // Handle failed login
        function handleFailedLogin() {
            showLoading(false);
            showError('Invalid User ID or Password. Please try again.');
            
            // Clear password field
            passwordInput.value = '';
            passwordInput.focus();
        }

        // Show loading state
        function showLoading(isLoading) {
            if (isLoading) {
                loginBtn.disabled = true;
                loginText.textContent = 'Logging in...';
                loginSpinner.classList.remove('hidden');
            } else {
                loginBtn.disabled = false;
                loginText.textContent = 'Login';
                loginSpinner.classList.add('hidden');
            }
        }

        // Enhanced error message with animation
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';

            // Trigger animation
            errorMessage.style.animation = 'none';
            setTimeout(() => {
                errorMessage.style.animation = 'shakeIn 0.5s ease-out';
            }, 10);

            // Add shake effect to login container
            document.querySelector('.login-container').style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                document.querySelector('.login-container').style.animation = '';
            }, 500);
        }

        // Enhanced success message with animation
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';

            // Trigger animation
            successMessage.style.animation = 'none';
            setTimeout(() => {
                successMessage.style.animation = 'bounceIn 0.6s ease-out';
            }, 10);
        }

        // Hide all messages
        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // Forgot password functionality
        function showForgotPassword() {
            alert('Please contact your administrator to reset your password.');
            return false;
            
        }

        // Enhanced auto-focus with animation delay
        window.addEventListener('load', function() {
            setTimeout(() => {
                userIdInput.focus();
                addInputAnimations();
            }, 1000); // Wait for container animation to complete
        });

        // Add input field animations and interactions
        function addInputAnimations() {
            const inputs = document.querySelectorAll('input');

            inputs.forEach(input => {
                // Add typing animation effect
                input.addEventListener('input', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });

                // Add focus ripple effect
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        }

        // Enter key handling
        userIdInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                passwordInput.focus();
            }
        });

        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });

        // Check if user is already logged in
        window.addEventListener('load', function() {
            if (sessionStorage.getItem('isLoggedIn') === 'true') {
                // Check if session is still valid (24 hours)
                const loginTime = new Date(sessionStorage.getItem('loginTime'));
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                
                if (hoursDiff < 24) {
                    window.location.href = 'main.html';
                } else {
                    // Session expired, clear storage
                    sessionStorage.clear();
                }
            }
        });
    </script>
</body>
</html>
