{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "compounds": [
        {
            "name": "Debug login.html",
            "configurations": ["Launch Chrome"]
        }
    ],
    "configurations": [
        {
            "name": "Launch Edge",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Attach to Chrome",
            "port": 9222,
            "request": "attach",
            "type": "chrome",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "file://${workspaceFolder}/login.html",
            "webRoot": "${workspaceFolder}",
            "runtimeArgs": ["--remote-debugging-port=9222"]
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "file://${workspaceFolder}/main.html",
            "webRoot": "${workspaceFolder}",
            "runtimeArgs": ["--remote-debugging-port=9222"]
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}"
        },

        {
            "name": "Attach",
            "port": 9222,
            "address": "localhost",
            "localRoot": "${workspaceFolder}",
            "remoteRoot": ".",
            "pathMapping": {
                "/": "${workspaceFolder}"
            },
            "port": 9222,
            "address": "localhost",
            "localRoot": "${workspaceFolder}",
            "remoteRoot": ".",
            "pathMapping": {
                "/": "${workspaceFolder}"
            },
            "request": "attach",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node"
        },
        {
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}"
            ],
            "name": "Launch Extension",
            "outFiles": [
                "${workspaceFolder}/out/**/*.js"
            ],
            "preLaunchTask": "npm",
            "request": "launch",
            "type": "extensionHost"
        },
        {
            "name": "Attach to Chrome",
            "port": 9222,
            "request": "attach",
            "type": "chrome",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "file://${workspaceFolder}/login.html",
            "webRoot": "${workspaceFolder}",
            "runtimeArgs": ["--remote-debugging-port=9222"]
        },
        {
            "name": "Attach to Edge",
            "port": 9222,
            "request": "attach",
            "type": "msedge",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Attach to Chrome",
            "port": 9222,
            "request": "attach",
            "type": "chrome",
            "webRoot": "${workspaceFolder}"
        },
        {
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}"
            ],
            "name": "Launch Extension",
            "outFiles": [
                "${workspaceFolder}/out/**/*.js"
            ],
            "preLaunchTask": "npm",
            "request": "launch",
            "type": "extensionHost"
        },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "file://${workspaceFolder}/login.html",
            "webRoot": "${workspaceFolder}",
            "runtimeArgs": ["--remote-debugging-port=9222"]
        },
        {
            "name": "Launch Edge",
            "request": "launch",
            "type": "msedge",
            "url": "file://${workspaceFolder}/login.html",
            "webRoot": "${workspaceFolder}",
            "runtimeArgs": ["--remote-debugging-port=9222"]
        },

        {
            "type": "msedge",
            "request": "launch",
            "name": "Open login.html",
            "file": "${workspaceFolder}/login.html"
        }
    ]
}

# Python 3
python -m http.server 8080

# Python 2
python -m SimpleHTTPServer 8080
