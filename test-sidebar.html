<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="script.js"></script>
    <script src="camera.js"></script>
    <script src="jewelry.js"></script>
    <script src="customer.js"></script>
    <script src="loan.js"></script>
    <script src="images.js"></script>
    <script src="totals.js"></script>
    <script src="print.js"></script>
    <script src="signature.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-reset.js"></script>
    <script src="form-submit.js"></script>
    <script src="form-compact.js"></script>
    <script src="form-responsive.js"></script>
    <script src="form-print.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
      <script src="form-enhancements.js"></script>
    <script src="form-compatibility.js"></script>
    <script src="form-accessibility.js"></script>
    <script src="form-optimization.js"></script>
    <script src="form-customization.js"></script>
    <script src="form-integration.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-analytics.js"></script>
    <script src="form-security.js"></script>
    <script src="form-performance.js"></script>
    <script src="form-a11y.js"></script>
    <script src="form-ux.js"></script>
    <script src="form-ui.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
    <script src="form-enhancements.js"></script>
        <style>
        /* General styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
            color: #e70505;
            line-height: 1.6;
            font-size: 20px;
            -webkit-print-color-adjust: exact; /* Ensure colors are printed correctly */
            -moz-print-color-adjust: exact; /* Ensure colors are printed correctly */
            print-color-adjust: exact; /* Ensure colors are printed correctly */
            background: linear-gradient(to right, #f0f0f0, #ffffff);
            color: #c90a0a; /* Dark red color for text */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            box-sizing: border-box; /* Ensure padding and borders are included in element's total width and height */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            font-size: 20px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            text-align: left; /* Align text to the left */
            padding: 20px; /* Add padding to the body */
            margin: 0; /* Remove default margin */
            background-color: #f8f9fa; /* Light background color */
            color: #000000; /* Dark text color for better contrast */
            text-align: left; /* Align text to the left */
            line-height: 1.5; /* Set line height for readability */
            font-size: 16px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            text :bold; /* Ensure text is not transformed */
            font-family: 'Helvetica Neue', Arial, sans-serif; /* Use a clean sans-serif font */
        }

        /* Sidebar styles */
        .sidebar {
        </style>
// Logout function - Fixed version
function logout() {
    console.log('Logout button clicked'); // Debug log
    
    // Show confirmation dialog
    const confirmLogout = confirm('Are you sure you want to logout?\n\nAny unsaved changes will be lost.');
    
    if (confirmLogout) {
        console.log('User confirmed logout'); // Debug log
        
        // Clear all session and local storage
        sessionStorage.clear();
        localStorage.clear();
        
        // Clear any cookies if they exist
        document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
        });
        
        // Show logout message
        const userInfoBar = document.querySelector('.user-info-bar');
        if (userInfoBar) {
            userInfoBar.innerHTML = '<div style="text-align: center; width: 100%; color: white; font-weight: bold;">Logging out...</div>';
        }
        
        // Force redirect to login page
        setTimeout(() => {
            console.log('Redirecting to login page'); // Debug log
            window.location.replace('login.html'); // Use replace instead of href
        }, 500);
    } else {
        console.log('User cancelled logout'); // Debug log
    }
}
// Alternative logout function if the above doesn't work
function forceLogout() {
    sessionStorage.clear();
    localStorage.clear();
    window.location.href = 'login.html';
}
// Make sure the function is globally available
window.logout = logout;
window.forceLogout = forceLogout;
    </script>
</body>
</html>