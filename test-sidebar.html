<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="script.js"></script>
    <script src="camera.js"></script>
    <script src="jewelry.js"></script>
    <script src="customer.js"></script>
    <script src="loan.js"></script>
    <script src="images.js"></script>
    <script src="totals.js"></script>
    <script src="print.js"></script>
    <script src="signature.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-reset.js"></script>
    <script src="form-submit.js"></script>
    <script src="form-compact.js"></script>
    <script src="form-responsive.js"></script>
    <script src="form-print.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
      <script src="form-enhancements.js"></script>
    <script src="form-compatibility.js"></script>
    <script src="form-accessibility.js"></script>
    <script src="form-optimization.js"></script>
    <script src="form-customization.js"></script>
    <script src="form-integration.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-analytics.js"></script>
    <script src="form-security.js"></script>
    <script src="form-performance.js"></script>
    <script src="form-a11y.js"></script>
    <script src="form-ux.js"></script>
    <script src="form-ui.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
    <script src="form-enhancements.js"></script>
        <style>
        /* General styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
            color: #e70505;
            line-height: 1.6;
            font-size: 20px;
            -webkit-print-color-adjust: exact; /* Ensure colors are printed correctly */
            -moz-print-color-adjust: exact; /* Ensure colors are printed correctly */
            print-color-adjust: exact; /* Ensure colors are printed correctly */
            background: linear-gradient(to right, #f0f0f0, #ffffff);
            color: #c90a0a; /* Dark red color for text */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            box-sizing: border-box; /* Ensure padding and borders are included in element's total width and height */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            font-size: 20px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            text-align: left; /* Align text to the left */
            padding: 20px; /* Add padding to the body */
            margin: 0; /* Remove default margin */
            background-color: #f8f9fa; /* Light background color */
            color: #000000; /* Dark text color for better contrast */
            text-align: left; /* Align text to the left */
            line-height: 1.5; /* Set line height for readability */
            font-size: 16px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            text :bold; /* Ensure text is not transformed */
            font-family: 'Helvetica Neue', Arial, sans-serif; /* Use a clean sans-serif font */
        }

        @media print {
            .container {
                border: 2px solid #333 !important;   /* Dark outline */
                border-radius: 10px !important;
                margin: 20px auto !important;        /* Margin around the box */
                padding: 16px !important;
                background: #fff !important;
                box-shadow: none !important;
                max-width: 95% !important;
            }
        }

        h1,
        h2 {
            text-align: center;
        }
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }
        .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 10px;
            max-width: 800px;
            margin: 0 auto 10px auto;
            padding: 0 15px;
        }
        .image-item {
            width: 220px;
            min-width: 200px;
            max-width: 240px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 15px 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .image-item label {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            width: 100%;
        }
        .image-item input[type="file"] {
            display: block;
            margin: 0 auto 4px auto;
            width: 100%;
            box-sizing: border-box;
            position: static;
            font-size: 11px;
            padding: 3px;
        }
        .image-item label {
            font-weight: bold;
            margin-bottom: 6px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }
        /* Customer Details Compact Layout */
        .customer-details-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }
        .customer-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 10px;
        }
        .customer-column-left,
        .customer-column-right {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .customer-details-grid .form-group {
            margin-bottom: 0;
        }
        .customer-details-grid .form-group label {
            font-size: 14px;
            margin-bottom: 4px;
        }
        .customer-details-grid .form-group input,
        .customer-details-grid .form-group textarea {
            padding: 8px;
            font-size: 14px;
        }
        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .customer-details-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .customer-details-compact {
                padding: 12px;
            }
        }
        /* Loan Information Compact Layout */
        .loan-info-compact {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .loan-info-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .loan-row-top {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        .loan-row-bottom {
            display: flex;
            width: 100%;
        }
        .amount-words-group {
            width: 100%;
        }
        .amount-words {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            min-height: 20px;
            font-style: italic;
            color: #666;
        }
        .loan-info-compact .form-group {
            margin-bottom: 0;
        }
        .loan-info-compact .form-group label {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        .loan-info-compact .form-group input {
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        /* Responsive design for loan info */
        @media (max-width: 768px) {
            .loan-row-top {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .loan-info-compact {
                padding: 12px;
            }
        }
        .file-info {
            font-size: 10px;
            color: #888;
            margin-bottom: 4px;
            width: 100%;
            text-align: center;
            border-radius: 4px;
            background: #f9f9f9;
            padding: 3px 0;
            line-height: 1.2;
        }
        .image-preview-box {
            margin-top: 8px;
            width: 100%;
            height: 120px;
            min-height: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            box-shadow: 0 2px 4px rgba(161, 53, 53, 0.1);
            padding: 5px;
            box-sizing: border-box;
            overflow: hidden;
        }
        #customerPhotoPreview,
        .jewelry-preview-img {
            max-width: 100% !important;
            max-height: 100% !important;
            width: auto !important;
            height: auto !important;
            object-fit: cover;
            border-radius: 6px;
            background: #fff;
            display: block;
            border: 1px solid #ccc;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            margin: 0 auto;
        }
        .image-preview-box img:hover {
            transform: scale(1.05);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-preview-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
            transition: all 0.2s ease;
        }

        /* Camera Controls */
        .camera-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 6px;
            width: 100%;
        }

        .camera-controls input[type="file"] {
            width: 100%;
            font-size: 11px;
            padding: 4px;
        }

        .camera-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
            align-self: center;
            width: fit-content;
        }

        .camera-btn:hover {
            background: #0056b3;
        }

        /* Camera Modal */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .camera-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-header h3 {
            margin: 0;
            color: #333;
        }

        .close-camera {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-camera:hover {
            color: #000;
        }

        #customerCameraVideo,
        #jewelryCameraVideo {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .camera-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .capture-btn,
        .retake-btn,
        .save-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .capture-btn {
            background: #28a745;
            color: white;
        }

        .retake-btn {
            background: #ffc107;
            color: #212529;
        }

        .save-btn {
            background: #007bff;
            color: white;
        }

        .capture-btn:hover {
            background: #218838;
        }

        .retake-btn:hover {
            background: #e0a800;
        }

        .save-btn:hover {
            background: #0056b3;
        }

        /* Images Section Compact Layout */
        .images-section-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .images-section-compact h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: #333;
        }

        /* Responsive design for images section */
        @media (max-width: 768px) {
            .image-container-box {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                width: 100%;
                padding: 0 10px;
                margin: 0 auto;
                max-width: 90%;
            }

            .images-section-compact {
                padding: 12px;
            }
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
            color: #999;
            font-size: 12px;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #ad0202;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 10mm;
    }

    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    .container {
        width: 190mm !important;
        max-width: 190mm !important;
        margin: 0 auto !important;
        padding: 5mm !important;
        text-align: center !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Center all form elements */
    .section-box {
        margin: 2mm auto !important;
        padding: 2mm !important;
        text-align: left !important;
        width: 100% !important;
    }

    /* Center header */
    .header-flex {
        justify-content: center !important;
        text-align: center !important;
        margin-bottom: 3mm !important;
    }

    /* Center images section */
    .image-container-box {
        justify-content: center !important;
        margin: 0 auto !important;
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
        .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    
    @media print {
        body {
            font-size: 8px !important; /* Adjust as needed */
        }
        .container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 5mm !important; /* Adjust as needed */
        }
        .header-flex {
            margin-bottom: 3mm !important;
        }
        .header-logo img {
            width: 20mm !important;
            height: 20mm !important;
        }
        .section-box {
            margin-bottom: 2mm !important;
            padding: 2mm !important;
        }
        .form-group {
            margin-bottom: 1mm !important;
        }
        .form-group label {
            margin-bottom: 0.5mm !important;
        }
        .image-preview-box img,
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 15mm !important;
            height: 15mm !important;
        }
        .weight-row {
            gap: 1mm !important;
        }
        .weight-item {
            padding: 1mm !important;
        }
        .weight-item label {
            font-size: 7px !important;
        }
        .weight-item input {
            font-size: 7px !important;
        }
        .signature-row {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-end !important;
            margin-top: 40px !important;
            width: 100% !important;
        }
        .signature-block {
            flex: 1 !important;
            padding: 0 10px !important;
            text-align: center !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:first-child {
            text-align: left !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:nth-child(2) {
            text-align: center !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:last-child {
            text-align: right !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
    }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-logo" style="margin-top: 20px;">
            <img src="https://20250608_130850.png" alt="Logo">
        </div>
        <h2>AMMAN GOLD FINANCE</h2>
        <ul class="menu">
            <li onclick="showSection('loan')" class="active">💰 Loan Creation</li>
            <li onclick="showSection('interest')">📊 Interest Creation</li>
            <li onclick="showSection('terms')">📄 Terms and Conditions</li>
            <li onclick="showSection('logout')">📈 Logout</li>
        </ul>
    </div>
    <script>
        /**
         * Show the specified section of the application by updating the content of the
         * page and highlighting the corresponding menu item.
         * @param {string} section The section to show. Can be 'loan', 'interest', or 'terms'.
            * @throws {Error} If the section is invalid.
            * @description This function updates the content area with the relevant section
            * and highlights the corresponding menu item.
            * @example
            * showSection('loan'); // Shows the Loan Creation section
            * showSection('interest'); // Shows the Interest Creation section
            * showSection('terms'); // Shows the Terms and Conditions section
            * @example
            * showSection('logout'); // Shows the Logout section
            * @example
            * showSection('report'); // Shows the Report section
            * @example
            * showSection('invalid'); // Throws an error for invalid section
            * @returns {void}
            *
            * <AUTHOR> Name]
            * @version 1.0
            * @since 2023-08-07
            * @see {@link https://example.com/documentation} for more details.

            * @see {@link https://example.com/issues} for known issues.
            * @see {@link https://example.com/changelog} for change log.
            * @see {@link https://example.com/contributing} for contribution guidelines.
         */
        function showSection(section) {
            const content = document.getElementById('content');
            // Remove active class from all menu items
            document.querySelectorAll('.menu li').forEach(li => li.classList.remove('active'));
            // Update content and highlight menu item based on section
            switch (section) {
                case 'loan':
                    content.innerHTML = "<h1>Loan Creation</h1><p>Welcome to the Loan Creation section. Fill in the details to create a new loan record.</p>";
                    document.querySelectorAll('.menu li')[0].classList.add('active');
                    break;
                case 'interest':
                    content.innerHTML = "<h1>Interest Creation</h1><p>Set interest rates and update them here.</p>";
                    document.querySelectorAll('.menu li')[1].classList.add('active');
                    break;
                case 'terms':
                    content.innerHTML = "<h1>Terms and Conditions</h1><p>View and update terms for loans.</p>";
                    document.querySelectorAll('.menu li')[2].classList.add('active');
                    break;
                default:
                    console.error(`Invalid section: ${section}`);
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="script.js"></script>
    <script src="camera.js"></script>
    <script src="jewelry.js"></script>
    <script src="customer.js"></script>
    <script src="loan.js"></script>
    <script src="images.js"></script>
    <script src="totals.js"></script>
    <script src="print.js"></script>
    <script src="signature.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-reset.js"></script>
    <script src="form-submit.js"></script>
    <script src="form-compact.js"></script>
    <script src="form-responsive.js"></script>
    <script src="form-print.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
      <script src="form-enhancements.js"></script>
    <script src="form-compatibility.js"></script>
    <script src="form-accessibility.js"></script>
    <script src="form-optimization.js"></script>
    <script src="form-customization.js"></script>
    <script src="form-integration.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-analytics.js"></script>
    <script src="form-security.js"></script>
    <script src="form-performance.js"></script>
    <script src="form-a11y.js"></script>
    <script src="form-ux.js"></script>
    <script src="form-ui.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
    <script src="form-enhancements.js"></script>
        <style>
        /* General styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
            color: #e70505;
            line-height: 1.6;
            font-size: 20px;
            -webkit-print-color-adjust: exact; /* Ensure colors are printed correctly */
            -moz-print-color-adjust: exact; /* Ensure colors are printed correctly */
            print-color-adjust: exact; /* Ensure colors are printed correctly */
            background: linear-gradient(to right, #f0f0f0, #ffffff);
            color: #c90a0a; /* Dark red color for text */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            box-sizing: border-box; /* Ensure padding and borders are included in element's total width and height */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            font-size: 20px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            text-align: left; /* Align text to the left */
            padding: 20px; /* Add padding to the body */
            margin: 0; /* Remove default margin */
            background-color: #f8f9fa; /* Light background color */
            color: #000000; /* Dark text color for better contrast */
            text-align: left; /* Align text to the left */
            line-height: 1.5; /* Set line height for readability */
            font-size: 16px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            text :bold; /* Ensure text is not transformed */
            font-family: 'Helvetica Neue', Arial, sans-serif; /* Use a clean sans-serif font */
        }

        @media print {
            .container {
                border: 2px solid #333 !important;   /* Dark outline */
                border-radius: 10px !important;
                margin: 20px auto !important;        /* Margin around the box */
                padding: 16px !important;
                background: #fff !important;
                box-shadow: none !important;
                max-width: 95% !important;
            }
        }

        h1,
        h2 {
            text-align: center;
        }
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }
        .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 10px;
            max-width: 800px;
            margin: 0 auto 10px auto;
            padding: 0 15px;
        }
        .image-item {
            width: 220px;
            min-width: 200px;
            max-width: 240px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 15px 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .image-item label {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            width: 100%;
        }
        .image-item input[type="file"] {
            display: block;
            margin: 0 auto 4px auto;
            width: 100%;
            box-sizing: border-box;
            position: static;
            font-size: 11px;
            padding: 3px;
        }
        .image-item label {
            font-weight: bold;
            margin-bottom: 6px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }
        /* Customer Details Compact Layout */
        .customer-details-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }
        .customer-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 10px;
        }
        .customer-column-left,
        .customer-column-right {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .customer-details-grid .form-group {
            margin-bottom: 0;
        }
        .customer-details-grid .form-group label {
            font-size: 14px;
            margin-bottom: 4px;
        }
        .customer-details-grid .form-group input,
        .customer-details-grid .form-group textarea {
            padding: 8px;
            font-size: 14px;
        }
        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .customer-details-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .customer-details-compact {
                padding: 12px;
            }
        }
        /* Loan Information Compact Layout */
        .loan-info-compact {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .loan-info-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .loan-row-top {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        .loan-row-bottom {
            display: flex;
            width: 100%;
        }
        .amount-words-group {
            width: 100%;
        }
        .amount-words {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            min-height: 20px;
            font-style: italic;
            color: #666;
        }
        .loan-info-compact .form-group {
            margin-bottom: 0;
        }
        .loan-info-compact .form-group label {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        .loan-info-compact .form-group input {
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        /* Responsive design for loan info */
        @media (max-width: 768px) {
            .loan-row-top {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .loan-info-compact {
                padding: 12px;
            }
        }
        .file-info {
            font-size: 10px;
            color: #888;
            margin-bottom: 4px;
            width: 100%;
            text-align: center;
            border-radius: 4px;
            background: #f9f9f9;
            padding: 3px 0;
            line-height: 1.2;
        }
        .image-preview-box {
            margin-top: 8px;
            width: 100%;
            height: 120px;
            min-height: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            box-shadow: 0 2px 4px rgba(161, 53, 53, 0.1);
            padding: 5px;
            box-sizing: border-box;
            overflow: hidden;
        }
        #customerPhotoPreview,
        .jewelry-preview-img {
            max-width: 100% !important;
            max-height: 100% !important;
            width: auto !important;
            height: auto !important;
            object-fit: cover;
            border-radius: 6px;
            background: #fff;
            display: block;
            border: 1px solid #ccc;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            margin: 0 auto;
        }
        .image-preview-box img:hover {
            transform: scale(1.05);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-preview-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
            transition: all 0.2s ease;
        }

        /* Camera Controls */
        .camera-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 6px;
            width: 100%;
        }

        .camera-controls input[type="file"] {
            width: 100%;
            font-size: 11px;
            padding: 4px;
        }

        .camera-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
            align-self: center;
            width: fit-content;
        }

        .camera-btn:hover {
            background: #0056b3;
        }

        /* Camera Modal */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .camera-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-header h3 {
            margin: 0;
            color: #333;
        }

        .close-camera {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-camera:hover {
            color: #000;
        }

        #customerCameraVideo,
        #jewelryCameraVideo {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .camera-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .capture-btn,
        .retake-btn,
        .save-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .capture-btn {
            background: #28a745;
            color: white;
        }

        .retake-btn {
            background: #ffc107;
            color: #212529;
        }

        .save-btn {
            background: #007bff;
            color: white;
        }

        .capture-btn:hover {
            background: #218838;
        }

        .retake-btn:hover {
            background: #e0a800;
        }

        .save-btn:hover {
            background: #0056b3;
        }

        /* Images Section Compact Layout */
        .images-section-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .images-section-compact h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: #333;
        }

        /* Responsive design for images section */
        @media (max-width: 768px) {
            .image-container-box {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                width: 100%;
                padding: 0 10px;
                margin: 0 auto;
                max-width: 90%;
            }

            .images-section-compact {
                padding: 12px;
            }
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
            color: #999;
            font-size: 12px;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #ad0202;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 10mm;
    }

    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    .container {
        width: 190mm !important;
        max-width: 190mm !important;
        margin: 0 auto !important;
        padding: 5mm !important;
        text-align: center !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Center all form elements */
    .section-box {
        margin: 2mm auto !important;
        padding: 2mm !important;
        text-align: left !important;
        width: 100% !important;
    }

    /* Center header */
    .header-flex {
        justify-content: center !important;
        text-align: center !important;
        margin-bottom: 3mm !important;
    }

    /* Center images section */
    .image-container-box {
        justify-content: center !important;
        margin: 0 auto !important;
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
        .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    
    @media print {
        body {
            font-size: 8px !important; /* Adjust as needed */
        }
        .container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 5mm !important; /* Adjust as needed */
        }
        .header-flex {
            margin-bottom: 3mm !important;
        }
        .header-logo img {
            width: 20mm !important;
            height: 20mm !important;
        }
        .section-box {
            margin-bottom: 2mm !important;
            padding: 2mm !important;
        }
        .form-group {
            margin-bottom: 1mm !important;
        }
        .form-group label {
            margin-bottom: 0.5mm !important;
        }
        .image-preview-box img,
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 15mm !important;
            height: 15mm !important;
        }
        .weight-row {
            gap: 1mm !important;
        }
        .weight-item {
            padding: 1mm !important;
        }
        .weight-item label {
            font-size: 7px !important;
        }
        .weight-item input {
            font-size: 7px !important;
        }
        .signature-row {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-end !important;
            margin-top: 40px !important;
            width: 100% !important;
        }
        .signature-block {
            flex: 1 !important;
            padding: 0 10px !important;
            text-align: center !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:first-child {
            text-align: left !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:nth-child(2) {
            text-align: center !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:last-child {
            text-align: right !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
    }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-logo" style="margin-top: 20px;">
            <img src="https://20250608_130850.png" alt="Logo">
        </div>
        <h2>AMMAN GOLD FINANCE</h2>
        <ul class="menu">
            <li onclick="showSection('loan')" class="active">💰 Loan Creation</li>
            <li onclick="showSection('interest')">📊 Interest Creation</li>
            <li onclick="showSection('terms')">📄 Terms and Conditions</li>
        </ul>
    </div>
    <script>
        /**
         * Show the specified section of the application by updating the content of the
         * page and highlighting the corresponding menu item.
         * @param {string} section The section to show. Can be 'loan', 'interest', or 'terms'.
         */
        function showSection(section) {
            const content = document.getElementById('content');
            // Remove active class from all menu items
            document.querySelectorAll('.menu li').forEach(li => li.classList.remove('active'));
            // Update content and highlight menu item based on section
            switch (section) {
                case 'loan':
                    content.innerHTML = "<h1>Loan Creation</h1><p>Welcome to the Loan Creation section. Fill in the details to create a new loan record.</p>";
                    document.querySelectorAll('.menu li')[0].classList.add('active');
                    break;
                case 'interest':
                    content.innerHTML = "<h1>Interest Creation</h1><p>Set interest rates and update them here.</p>";
                    document.querySelectorAll('.menu li')[1].classList.add('active');
                    break;
                case 'terms':
                    content.innerHTML = "<h1>Terms and Conditions</h1><p>View and update terms for loans.</p>";
                    document.querySelectorAll('.menu li')[2].classList.add('active');
                    break;
                case 'logout':
                    content.innerHTML = "<h1>Logout</h1><p>You have successfully logged out.</p>";
                    document.querySelectorAll('.menu li')[3].classList.add('active');
                    break;
                    case 'report':
                    content.innerHTML = "<h1>Report</h1><p>View and generate reports here.</p>";
                    document.querySelectorAll('.menu li')[4].classList.add('active');
                    break;
                default:
                    console.error(`Invalid section: ${section}`);
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/print-js/1.6.0/print.min.js"></script>
    <script src="script.js"></script>
    <script src="camera.js"></script>
    <script src="jewelry.js"></script>
    <script src="customer.js"></script>
    <script src="loan.js"></script>
    <script src="images.js"></script>
    <script src="totals.js"></script>
    <script src="print.js"></script>
    <script src="signature.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-reset.js"></script>
    <script src="form-submit.js"></script>
    <script src="form-compact.js"></script>
    <script src="form-responsive.js"></script>
    <script src="form-print.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
      <script src="form-enhancements.js"></script>
    <script src="form-compatibility.js"></script>
    <script src="form-accessibility.js"></script>
    <script src="form-optimization.js"></script>
    <script src="form-customization.js"></script>
    <script src="form-integration.js"></script>
    <script src="form-validation.js"></script>
    <script src="form-analytics.js"></script>
    <script src="form-security.js"></script>
    <script src="form-performance.js"></script>
    <script src="form-a11y.js"></script>
    <script src="form-ux.js"></script>
    <script src="form-ui.js"></script>
    <script src="form-helpers.js"></script>
    <script src="form-utilities.js"></script>
    <script src="form-animations.js"></script>
    <script src="form-enhancements.js"></script>
        <style>
        /* General styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
            color: #e70505;
            line-height: 1.6;
            font-size: 20px;
            -webkit-print-color-adjust: exact; /* Ensure colors are printed correctly */
            -moz-print-color-adjust: exact; /* Ensure colors are printed correctly */
            print-color-adjust: exact; /* Ensure colors are printed correctly */
            background: linear-gradient(to right, #f0f0f0, #ffffff);
            color: #c90a0a; /* Dark red color for text */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            box-sizing: border-box; /* Ensure padding and borders are included in element's total width and height */
            overflow-x: hidden; /* Prevent horizontal scrolling */
            font-size: 20px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            text-align: left; /* Align text to the left */
            padding: 20px; /* Add padding to the body */
            margin: 0; /* Remove default margin */
            background-color: #f8f9fa; /* Light background color */
            color: #000000; /* Dark text color for better contrast */
            text-align: left; /* Align text to the left */
            line-height: 1.5; /* Set line height for readability */
            font-size: 16px; /* Set base font size */
            font-weight: 400; /* Set base font weight */
            -webkit-font-smoothing: antialiased; /* Improve font rendering */
            -moz-osx-font-smoothing: grayscale; /* Improve font rendering */
            text :bold; /* Ensure text is not transformed */
            font-family: 'Helvetica Neue', Arial, sans-serif; /* Use a clean sans-serif font */
        }

        @media print {
            .container {
                border: 2px solid #333 !important;   /* Dark outline */
                border-radius: 10px !important;
                margin: 20px auto !important;        /* Margin around the box */
                padding: 16px !important;
                background: #fff !important;
                box-shadow: none !important;
                max-width: 95% !important;
            }
        }

        h1,
        h2 {
            text-align: center;
        }
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 5px 8px;
            font-size: 13px;
            height: 28px;
            width: calc(100% - 20px);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .form-group select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;
            background-size: 10px;
        }
        .image-container-box {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 10px;
            max-width: 800px;
            margin: 0 auto 10px auto;
            padding: 0 15px;
        }
        .image-item {
            width: 220px;
            min-width: 200px;
            max-width: 240px;
            background: #fff;
            border: 1.5px solid #222;
            border-radius: 10px;
            padding: 15px 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .image-item label {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            width: 100%;
        }
        .image-item input[type="file"] {
            display: block;
            margin: 0 auto 4px auto;
            width: 100%;
            box-sizing: border-box;
            position: static;
            font-size: 11px;
            padding: 3px;
        }
        .image-item label {
            font-weight: bold;
            margin-bottom: 6px;
            width: 100%;
            text-align: left;
            font-size: 16px;
        }
        /* Customer Details Compact Layout */
        .customer-details-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }
        .customer-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 10px;
        }
        .customer-column-left,
        .customer-column-right {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .customer-details-grid .form-group {
            margin-bottom: 0;
        }
        .customer-details-grid .form-group label {
            font-size: 14px;
            margin-bottom: 4px;
        }
        .customer-details-grid .form-group input,
        .customer-details-grid .form-group textarea {
            padding: 8px;
            font-size: 14px;
        }
        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .customer-details-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .customer-details-compact {
                padding: 12px;
            }
        }
        /* Loan Information Compact Layout */
        .loan-info-compact {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .loan-info-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .loan-row-top {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        .loan-row-bottom {
            display: flex;
            width: 100%;
        }
        .amount-words-group {
            width: 100%;
        }
        .amount-words {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            min-height: 20px;
            font-style: italic;
            color: #666;
        }
        .loan-info-compact .form-group {
            margin-bottom: 0;
        }
        .loan-info-compact .form-group label {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        .loan-info-compact .form-group input {
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        /* Responsive design for loan info */
        @media (max-width: 768px) {
            .loan-row-top {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .loan-info-compact {
                padding: 12px;
            }
        }
        .file-info {
            font-size: 10px;
            color: #888;
            margin-bottom: 4px;
            width: 100%;
            text-align: center;
            border-radius: 4px;
            background: #f9f9f9;
            padding: 3px 0;
            line-height: 1.2;
        }
        .image-preview-box {
            margin-top: 8px;
            width: 100%;
            height: 120px;
            min-height: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            box-shadow: 0 2px 4px rgba(161, 53, 53, 0.1);
            padding: 5px;
            box-sizing: border-box;
            overflow: hidden;
        }
        #customerPhotoPreview,
        .jewelry-preview-img {
            max-width: 100% !important;
            max-height: 100% !important;
            width: auto !important;
            height: auto !important;
            object-fit: cover;
            border-radius: 6px;
            background: #fff;
            display: block;
            border: 1px solid #ccc;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            margin: 0 auto;
        }
        .image-preview-box img:hover {
            transform: scale(1.05);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-preview-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
            transition: all 0.2s ease;
        }

        /* Camera Controls */
        .camera-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 6px;
            width: 100%;
        }

        .camera-controls input[type="file"] {
            width: 100%;
            font-size: 11px;
            padding: 4px;
        }

        .camera-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
            align-self: center;
            width: fit-content;
        }

        .camera-btn:hover {
            background: #0056b3;
        }

        /* Camera Modal */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .camera-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-header h3 {
            margin: 0;
            color: #333;
        }

        .close-camera {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-camera:hover {
            color: #000;
        }

        #customerCameraVideo,
        #jewelryCameraVideo {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .camera-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .capture-btn,
        .retake-btn,
        .save-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .capture-btn {
            background: #28a745;
            color: white;
        }

        .retake-btn {
            background: #ffc107;
            color: #212529;
        }

        .save-btn {
            background: #007bff;
            color: white;
        }

        .capture-btn:hover {
            background: #218838;
        }

        .retake-btn:hover {
            background: #e0a800;
        }

        .save-btn:hover {
            background: #0056b3;
        }

        /* Images Section Compact Layout */
        .images-section-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .images-section-compact h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: #333;
        }

        /* Responsive design for images section */
        @media (max-width: 768px) {
            .image-container-box {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                width: 100%;
                padding: 0 10px;
                margin: 0 auto;
                max-width: 90%;
            }

            .images-section-compact {
                padding: 12px;
            }
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
            color: #999;
            font-size: 12px;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #ad0202;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 10mm;
    }

    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    .container {
        width: 190mm !important;
        max-width: 190mm !important;
        margin: 0 auto !important;
        padding: 5mm !important;
        text-align: center !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Center all
            }        /* Signature row adjustments */              .signature-row {            display: flex;            justify-content: space-between;            align-items: flex-end;            margin-top: 40px;            width: 100%;            gap: 10px;        }                .signature-block {            flex: 1;            padding: 0 10px;        }                .signature-label {            font-weight: bold;            margin-bottom: 30px;            font-size: 13px;            text-align: center;        }                .signature-line {            border-bottom: 1px solid #333;            margin: 30px 0 0 0;            height: 30px;            width: 100%;            display: block;        }            @media print {            .signature-row,            .signature-block,            .signature-label,            .signature-block input[type="text"] {                display: block !important;                visibility: visible !important;                height: auto !important;                width: 100% !important;                color: #000 !important;            }            .signature-row {                position: static !important;                margin-top: 40px !important;                page-break-inside: avoid !important;            }            .signature-block input[type="text"] {                border: 1px solid #333 !important;                background: #fff !important;                margin-top: 8px !important;                width: 80% !important;                max-width: 220px !important;            }    /* Add more specific print styles here */}    </style></head><body>    <!-- Place this inside your .container at the top --><div class="header-flex header-center">    <div class="header-logo">        <img src="20250608_130850.png" alt="Amman Gold Finance Logo">    </div>    <div class="header-details">        <h1>AMMAN GOLD FINANCE</h1>        <div><b>REGISTERED OFFICE: NO 2/4-4, S.V.A. EXTENSION - 4, OPPOSITE GOVERNMENT GIRLS HIGH SCHOOL,</b></div>        <div><b>TIRUCHENGODE - 637211, NAMAKKAL DISTRICT.</b></div>        <div><b>GST NO: 33AAGFA6262H1Z3</b></div>        <div><b>REGISTERED UNDER THE TAMIL NADU PAWN BROKERS ACT,1943.</b></div>        <div><b>LICENCE NO: 2020250401145</b></div>        <div><b><E-mail>E-mail:<EMAIL></E-mail>: </b></div>    </div></div><style>.header-flex.header-center {    justify-content: center;    text-align: center;    gap: 30px;}.header-flex.header-center .header-logo {    display: flex;    justify-content: center;    align-items: center;}.header-flex.header-center .header-details {    text-align: center;    align-items: center;    display: flex;    flex-direction: column;}.header-flex.header-center .header-details h1 {    text-align: center;}.header-flex.header-center .header-details div {    text-align: center;    display: block;    width: 100%;}@media (max-width: 700px) {    .header-flex.header-center {        flex-direction: column;        gap: 10px;    }    .header-flex.header-center .header-logo img {        margin: 0 auto;    }}</style>    <div class="container">        <!-- Customer Information -->        <div class="section-box customer-details-compact">            <!-- Customer Details -->            <h2>Customer Details</h2>            <div class="customer-details-grid">                <!-- Left Column -->                <div class="customer-column-left">                    <div class="form-group">                        <label for="name">Full Name:</label>                        <input type="text" id="name" placeholder="John Doe">                    </div>                    <div class="form-group">                        <label for="address">Address:</label>                        <textarea id="address" rows="2" placeholder="123 Gold Street, City"></textarea>                    </div>                    <div class="form-group">                        <label for="pincode">Pincode:</label>                        <input type="text" id="pincode" placeholder="123456">                    </div>                    <div class="form-group">                        <label for="landmark">Landmark:</label>                        <input type="text" id="landmark" placeholder="Landmark">                    </div>                </div>                <!-- Right Column -->                <div class="customer-column-right">                    <div class="form-group">                        <label for="idProof">ID Proof:</label>                        <input type="text" id="idProof" placeholder="Aadhar Card / Voter ID / Pan Card / Passport">                    </div>                    <div class="form-group">                        <label for="email">Email:</label>                        <input type="email" id="email" placeholder="<EMAIL>">                    </div>                    <div class="form-group">                        <label for="dob">Date of Birth:</label>                        <input type="date" id="dob">                    </div>                    <div class="form-group">                        <label for="contact">Contact Number:</label>                        <input type="tel" id="contact" placeholder="1234567890">                    </div>                </div>            </div>                        <!-- Loan Information Section -->            <div class="loan-info-compact">                <div class="loan-info-grid">                    <!-- Top Row - Loan Amount and Dates -->                    <div class="loan-row-top">                        <div class="form-group">                            <label for="loanAmount">Loan Amount:</label>                            <input type="number" id="loanAmount" placeholder="0.00" min="0" step="0.01">                        </div>                        <div class="form-group">                            <label for="loanDate">Loan Date:</label>                            <input type="date" id="loanDate">                        </div>                        <div class="form-group">                            <label for="renewalDate">Last Renewal Date:</label>                            <input type="date" id="renewalDate">                        </div>                        <div class="form-group">                            <label for="Schemename">Scheme Name</label>                            <select id="Scheme Name">                                <option value="select">Select</option>                                <option value="AGL 0.90 Paise">AGL 0.90 Paise</option>                                <option value="AGL 1.00 RUPEE">AGL 1.00 "</option>                                <option value="MGL 1.25 RUPEE">MGL 1.25 </option>                                <option value="HGL 1.5 RUPEE">HGL 1.5 </option>                                <option value="HGL+ 1.80 RUPEE">HGL+ 1.80 </option>                                <option value="EGL 2.00 RUPEE">EGL 2.00 </option>                                <option value="EGL 2.10 RUPEE">EGL 2.10 </option>                                <option value="EGL+ 2.50 RUPEE">EGL+ 2.50 </option>                            </select>                        </div>                    </div>                    <!-- Bottom Row - Amount in Words -->                    <div class="loan-row-bottom">                        <div class="form-group amount-words-group">                            <label>Amount in Words:</label>                            <div id="loanAmountWords" class="amount-words"></div>                        </div>                    </div>                </div>            </div>        </div>        <!-- Image upload section -->        <div class="section-box images-section-compact">            <h2>Images</h2>            <div class="image-container-box">                <!-- Customer Photo -->                <div class="image-item">                    <label>Customer Photo:</label>                    <div class="camera-controls">                        <input type="file" id="customerPhotoInput" accept="image/*">                        <button type="button" id="customerCameraBtn" class="camera-btn">📷 Camera</button>                    </div>                    <div id="customerFileInfo" class="file-info">No file chosen</div>                    <!-- Camera Modal -->                    <div id="customerCameraModal" class="camera-modal" style="display: none;">                        <div class="camera-content">                            <div class="camera-header">                                <h3>Take Customer Photo</h3>                                <button type="button" id="customerCloseCamera" class="close-camera">×</button>                            </div>                            <video id="customerCameraVideo" autoplay playsinline></video>                            <canvas id="customerCameraCanvas" style="display: none;"></canvas>                            <div class="camera-buttons">                                <button type="button" id="customerCaptureBtn" class="capture-btn">📸 Capture</button>                                <button type="button" id="customerRetakeBtn" class="retake-btn" style="display: none;">🔄 Retake</button>                                <button type="button" id="customerSaveBtn" class="save-btn" style="display: none;">✓ Save</button>                            </div>                        </div>                    </div>                    <div class="image-preview-box">                        <div id="customerPhotoPlaceholder" class="image-placeholder hide-on-print">                            <span style="font-size: 7px;">Upload Customer Photo</span>                        </div>                        <img id="customerPhotoPreview" alt="Customer Photo" style="display: none;">                    </div>                </div>                                <!-- Jewelry Photos -->                <div class="image-item">                    <label>Jewelry Photos:</label>                    <div class="camera-controls">                        <input type="file" id="jewelryPhotoInput" accept="image/*" multiple>                        <button type="button" id="jewelryCameraBtn" class="camera-btn">📷 Camera</button>                    </div>                    <div id="jewelryFileInfo" class="file-info">No file chosen</div>                    <!-- Camera Modal -->                    <div id="jewelryCameraModal" class="camera-modal" style="display: none;">                        <div class="camera-content">                            <div class="camera-header">                                <h3>Take Jewelry Photo</h3>                                <button type="button" id="jewelryCloseCamera" class="close-camera">×</button>                            </div>                            <video id="jewelryCameraVideo" autoplay playsinline></video>                            <canvas id="jewelryCameraCanvas" style="display: none;"></canvas>                            <div class="camera-buttons">                                <button type="button" id="jewelryCaptureBtn" class="capture-btn">📸 Capture</button>                                <button type="button" id="jewelryRetakeBtn" class="retake-btn" style="display: none;">🔄 Retake</button>                                <button type="button" id="jewelrySaveBtn" class="save-btn" style="display: none;">✓ Save</button>                            </div>                        </div>                    </div>                    <div class="image-preview-box">                        <div id="jewelryPreviewContainer">                            <div id="jewelryPhotoPlaceholder" class ="image-placeholder hide-on-print">                                <span style="font-size: 7px;">Upload Jewelry Images</span>                            </div>                        </div>                    </div>                </div>            </div>        </div>        <!-- Single Jewelry Details section with proper structure -->        <div class="section-box">            <h2>Jewelry Details</h2>            <div id="jewelryItemsList">                <!-- Initial jewelry item will be added here by JavaScript -->            </div>            <!-- Totals calculation row -->              <div class="jewelry-totals">                <h3>Totals</h3>                <div class="weight-row totals-row">                    <div class="weight-item">                        <label for="totalJewelryCount">Total Jewelry Count:</label>                        <input type="number" id="totalJewelryCount" readonly>                    </div>                    <div class="weight-item">                        <label for="totalJewelryWeight">Total Weight (grams):</label>                        <input type="number" id="totalJewelryWeight" readonly>                    </div>                    <div class="weight-item">                        <label for="totalStoneWeight">Total Stone Weight (grams):</label>                        <input type="number" id="totalStoneWeight" readonly>                    </div>                    <div class="weight-item">                        <label for="totalNetWeight">Total Net Weight (grams):</label>                        <input type="number" id="totalNetWeight" readonly>                    </div>                </div>            </div>                        <div class="add-item-container">                <button type="button" id="addJewelryItemBtn" class="add-item-btn hide-on-print">Add Jewelry Item</button>            </div>        </div>              <!-- Place this after the totals section, before </div> of .container -->        <!-- Print button -->        <div class="section-box">            <button class="print-btn header-center" onclick="printDocument()">Print Document</button>              </div>        <div class="signature-row">            <div class="signature-block left">                <label class="signature-label" for="BranchManagerSignature">Branch Manager Signature</label>            </div>            <div class="signature-block center">                <label class="signature-label" for="CashierSignature">Cashier Signature</label>            </div>            <div class="signature-block right">                <label class="signature-label" for="CustomerSignature">Customer Signature</label>            </div>        </div>    </div>    <script>        // Function to add a new jewelry item        function addJewelryItem() {            const jewelryItemsList = document.getElementById('jewelryItemsList');            const itemCount = jewelryItemsList.children.length + 1;            const jewelryItemDiv = document.createElement('div');            jewelryItemDiv.className = 'jewelry-item';            jewelryItemDiv.innerHTML = `                <div class="weight-row">                    <div class="weight-item">                        <label for="jewelryCount_0">Jewelry Count:</label>                        <input type="number" id="jewelryCount_0" class="jewelry-count" min="1" value="1">                    </div>                    <div class="weight-item">                        <label for="jewelryWeight_0">Gross Weight (grams):</label>                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">                        <label for                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">                        </div>                    <div class="weight-item">                        <label for="stoneWeight_0">Stone Weight (grams):</label>                        <input type="number" id="stoneWeight_0" class="stone-weight" min="0" step="0.1">                    </div>                    <div class="weight-item">                        <label for="netWeight_0">Net Weight (grams):</label>                        <input type="number" id="netWeight_0" class="net-weight" min="0" step="0.1" readonly>                    </div>                </div>                <div class="jewelry-type-row">                    <div class="jewelry-selection">                        <label for="jewelryType_0">Jewelry Type:</label>                        <select id="jewelryType_0" class="jewelry-type">                           <option value="">Select Jewelry Type</option>                            <option value="gold-chain">Gold Chain</option>                            <option value="gold-ring">Gold Ring</option>                            <option value="gold-bracelet">Gold Bracelet</option>                            <option value="gold-necklace">Gold Necklace</option>                            <option value="gold-earrings">Gold Earrings</option>                            <option value="gold-bangles">Gold Bangles</option>                            <option value="gold-pendants">Gold Pendants</option>                            <option value="gold-ear-studs">Gold Ear Studs</option>                            <option value="gold-studs">Gold Studs</option>                            <option value="gold-stone studs ">Gold Stone Studs</option>                            <option value="gold-mattal">Gold Mattal</option>                            <option value="gold-baby studs">Gold Baby Studs</option>                            <option value="gold-lockets">Gold Lockets</option>                            <option value="gold-anklets">Gold Anklets</option>                            <option value="gold-manga pich">Gold Manga Pich</option>                            <option value="gold-talikasu">Gold Talikasu </option>                            <option value="gold-nose pin">Gold Nose Pin</option>                            <option value="gold-thali kundu">Gold Thali Kundu</option>                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>                            <option value="gold-gold coin">Gold Coin</option>                            <option value="silver-items">Silver Items</option>                        </select>                    </div>                    <div class="purity-selection">                        <label for="purityType_0">Purity Type:</label>                        <select id="purityType_0" class="purity-type">                            <option value="">Select Purity</option>                            <option value="24k">24K (99.9%)</option>                            <option value="22k">22K (91.6%)</option>                            <option value="18k">18K (75.0%)</option>                            <option value="14k">14K (58.3%)</option>                            <option value="10k">10K (41.7%)</option>                        </select>                    </div>                                        <div class="item-actions">                        <button type="button" class="remove-item-btn hide-on-print" data-index="0" style="display: none;">Remove</button>                    </div>                </div>            `;                        jewelryItemsList.appendChild(jewelryItemDiv);                        // Initialize net weight calculation for the initial item            initializeNetWeightCalculation(0);        }                // Function to add a new jewelry item        function addJewelryItem() {            const jewelryItemsList = document.getElementById('jewelryItemsList');            if (!jewelryItemsList) return;            const itemCount = jewelryItemsList.querySelectorAll('.jewelry-item').length;            const newIndex = itemCount;            const newItem = document.createElement('div');            newItem.className = 'jewelry-item';            newItem.innerHTML = `                <div class="weight-row">                    <div class="weight-item">                        <label for="jewelryCount_${newIndex}">Jewelry Count:</label>                        <input type="number" id="jewelryCount_${newIndex}" class="jewelry-count" min="1" value="1">                    </div>                    <div class="weight-item">                        <label for="jewelryWeight_${newIndex}">Gross Weight (grams):</label>                        <input type="number" id="jewelryWeight_${newIndex}" class="jewelry-weight" min="0" step="0.1">                    </div>                    <div class="weight-item">                        <label for="stoneWeight_${newIndex}">Stone Weight (grams):</label>                        <input type="number" id="stoneWeight_${newIndex}" class="stone-weight" min="0" step="0.1">                    </div>                    <div class="weight-item">                        <label for="netWeight_${newIndex}">Net Weight (grams):</label>                        <input type="number" id="netWeight_${newIndex}" class="net-weight" min="0" step="0.1" readonly>                    </div>                </div>                <div class="jewelry-type-row">                    <div class="jewelry-selection">                        <label for="jewelryType_${newIndex}">Jewelry Type:</label>                        <select id="jewelryType_${newIndex}" class="jewelry-type">                            <option value="">Select Jewelry Type</option>                            <option value="gold-chain">Gold Chain</option>                            <option value="gold-ring">Gold Ring</option>                            <option value="gold-bracelet">Gold Bracelet</option>                            <option value="gold-necklace">Gold Necklace</option>                            <option value="gold-earrings">Gold Earrings</option>                            <option value="gold-bangles">Gold Bangles</option>                            <option value="gold-pendants">Gold Pendants</option>                            <option value="gold-ear-studs">Gold Ear Studs</option>                            <option value="gold-studs">Gold Studs</option>                            <option value="gold-stone studs ">Gold Stone Studs</option>                            <option value="gold-mattal">Gold Mattal</option>                            <option value="gold-baby studs">Gold Baby Studs</option>                            <option value="gold-lockets">Gold Lockets</option>                            <option value="gold-anklets">Gold Anklets</option>                            <option value="gold-manga pich">Gold Manga Pich</option>                            <option value="gold-talikasu">Gold Talikasu </option>                            <option value="gold-nose pin">Gold Nose Pin</option>                            <option value="gold-thali kundu">Gold Thali Kundu</option>                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>                            <option value="gold-gold coin">Gold Coin</option>                            <option value="silver-items">Silver Items</option>                        </select>                    </div>                    <div class="purity-selection">                        <label for="purityType_${newIndex}">Purity Type:</label>                        <select id="purityType_${newIndex}" class="purity-type">                            <option value="">Select Purity</option>                            <option value="24k">24K (99.9%)</option>                            <option value="22k">22K (91.6%)</option>                            <option value="18k">18K (75.0%)</option>                            <option value="14k">14K (58.3%)</option>                            <option value="10k">10K (41.7%)</option>                        </select>                    </div>                    <div class="item-actions">                        <button type="button" class="remove-item-btn hide-on-print" data-index="${newIndex}">Remove</button>                    </div>                </div>            `;            jewelryItemsList.appendChild(newItem);            // Hide remove button only for the first item            const removeBtn = newItem.querySelector('.remove-item-btn');            if (removeBtn) {                if (newIndex === 0) {                    removeBtn.style.display = 'none';                } else {                    removeBtn.style.display = 'inline-block';                }                removeBtn.addEventListener('click', function() {                    removeJewelryItem(newIndex);                });            }            // Attach event listeners for calculation            newItem.querySelector('.jewelry-count').addEventListener('input', calculateTotals);            newItem.querySelector('.jewelry-weight').addEventListener('input', calculateTotals);            newItem.querySelector('.stone-weight').addEventListener('input', calculateTotals);            // Initialize net weight calculation for the new item            initializeNetWeightCalculation(newIndex);            // Show the remove button on the first item if we have more than one item            if (jewelryItemsList.querySelectorAll('.jewelry-item').length > 1) {                const firstItemRemoveBtn = jewelryItemsList.querySelector('.jewelry-item:first-child .remove-item-btn');                if (firstItemRemoveBtn) {                    firstItemRemoveBtn.style.display = 'inline-block';                }            }            calculateTotals();        }                // Function to remove a jewelry item        function removeJewelryItem(index) {            console.log(`Removing jewelry item with index: ${index}`);                        const jewelryItemsList = document.getElementById('jewelryItemsList');            const items = jewelryItemsList.querySelectorAll('.jewelry-item');                        // Don't remove if it's the last item            if (items.length <= 1) {                console.log("Cannot remove the last item");                return;            }                        console.log(`Total items before removal: ${items.length}`);            console.log(`Attempting to remove item with index: ${index}`);            // Check if the index is valid            if (index < 0 || index >= items.length) {                console.error(`Invalid index: ${index}. Cannot remove item.`);                return;            }            console.log(`Valid index: ${index}. Proceeding with removal.`);            // Log the current items for debugging            items.forEach((item, i) => {                console.log(`Item ${i}:`, item);            });                        console.log(`Items before removal: ${items.length}`);            // Log the remove button for debugging            const removeBtn = document.querySelector(`.remove-item-btn[data-index="${index}"]`);            if (removeBtn) {                console.log("Remove button found:", removeBtn);            } else {                console.error(`Remove button with index ${index} not found`);            }            // Log the jewelry items list for debugging            console.log("Jewelry items list:", jewelryItemsList);            // Log the items in the jewelry items list            console.log("Items in jewelry items list:", items);                console.log(`Items in jewelry items list: ${items.length}`);            // Log the jewelry items list container            console.log("Jewelry items list container:", jewelryItemsList);            // Log the current state of the jewelry items list            console.log("Current jewelry items list state:", jewelryItemsList.innerHTML);            // Find the item with the matching index and remove it            for (let i = 0; i < items.length; i++) {                const removeBtn = items[i].querySelector('.remove-item-btn');                if (removeBtn && removeBtn.getAttribute('data-index') == index) {                    items[i].remove();                    console.log(`Item with index ${index} removed`);                    break;                } else {                    console.error(`Remove button with index ${index} not found`);                    console.error(`Remove button with index ${index} not found`);                    removeBtn.classList.add('hide-on-print');                    removeBtn.style.display = 'none';                }            }                        // Hide the remove button on the first item if we only have one item left            if (jewelryItemsList.querySelectorAll('.jewelry-item').length === 1) {                const firstItemRemoveBtn = document.querySelector('.jewelry-item:first-child .remove-item-btn');                if (firstItemRemoveBtn) {                    firstItemRemoveBtn.style.display = 'none';                    console.log("First item remove button hidden");                    firstItemRemoveBtn.classList.add('hide-on-print');                } else {                    console.error("First item remove button not found");                    firstItemRemoveBtn.classList.add('hide-on-print');                    firstItemRemoveBtn.style.display = 'none';                    firstItemRemoveBtn.classList.add('hide-on-print');                                    }            }                        // Calculate totals after removing an item            calculateTotals();        }                // Function to initialize net weight calculation        function initializeNetWeightCalculation(index) {            const jewelryWeightInput = document.getElementById(`jewelryWeight_${index}`);            const stoneWeightInput = document.getElementById(`stoneWeight_${index}`);            const netWeightInput = document.getElementById(`netWeight_${index}`);                        if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {                console.error(`Could not find weight inputs for index ${index}`);                return;            }                        // Function to calculate net weight            function calculateNetWeight() {                const jewelryWeight = parseFloat(jewelryWeightInput.value) || 0;                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;                const netWeight = jewelryWeight - stoneWeight;                netWeightInput.value = netWeight > 0 ? netWeight.toFixed(1) : 0;                                // Update totals whenever an individual weight changes                calculateTotals();            }                        // Add event listeners for weight inputs            jewelryWeightInput.addEventListener('input', calculateNetWeight);            stoneWeightInput.addEventListener('input', calculateNetWeight);        }                // Function to calculate totals from all jewelry items        function calculateTotals() {            console.log("Calculating totals");            let totalJewelryCount = 0;            let totalJewelryWeight = 0;            let totalStoneWeight = 0;            let totalNetWeight = 0;                        // Get all jewelry items            const jewelryItems = document.querySelectorAll('.jewelry-item');                         // Calculate totals    jewelryItems.forEach(item => {        const jewelryCount = parseFloat(item.querySelector('.jewelry-count')?.value) || 0;        const jewelryWeight = parseFloat(item.querySelector('.jewelry-weight')?.value) || 0;        const stoneWeight = parseFloat(item.querySelector('.stone-weight')?.value) || 0;        const netWeight = parseFloat(item.querySelector('.net-weight')?.value) || 0;        totalJewelryCount += jewelryCount;        totalJewelryWeight += jewelryWeight;        totalStoneWeight += stoneWeight;        totalNetWeight += netWeight;    });                      // Update total fields    document.getElementById('totalJewelryCount').value = totalJewelryCount;    document.getElementById('totalJewelryWeight').value = totalJewelryWeight.toFixed(1);    document.getElementById('totalStoneWeight').value = totalStoneWeight.toFixed(1);    document.getElementById('totalNetWeight').value = totalNetWeight.toFixed(1);    console.log(`Totals calculated - Count: ${totalJewelryCount}, Jewelry: ${totalJewelryWeight}, Stone: ${totalStoneWeight}, Net: ${totalNetWeight}`);}                // Print functionality        function initializePrintFunctionality() {            // First, remove any existing event listeners by replacing all buttons            const printBtns = document.querySelectorAll('.print-btn');            printBtns.forEach(btn => {                // Create a completely new button                const newBtn = document.createElement('button');                newBtn.className = 'print-btn';                newBtn.textContent = 'Print Document';                newBtn.type = 'button';                newBtn.id = 'printBtn';                newBtn.style.display = 'inline-block';                newBtn.style.margin = '10px 0';                newBtn.style.padding = '10px 20px';                newBtn.style.backgroundColor = '#4CAF50';                newBtn.style.color = 'white';                newBtn.style.border = 'chocolate 1px solid';                newBtn.style.borderRadius = '5px';                newBtn.style.cursor = 'pointer';                newBtn.style.fontSize = '16px';                newBtn.style.fontWeight = 'bold';                newBtn.style.textAlign = 'center';                                // Replace the old button                if (btn.parentNode) {                    btn.parentNode.replaceChild(newBtn, btn);                }            });                        // Now add a single event listener to the new button            const newPrintBtn = document.querySelector('.print-btn');            if (newPrintBtn) {                // Use a named function for clarity                newPrintBtn.addEventListener('click', singlePrintFunction);            }                        // Remove any inline onclick attributes from the HTML            document.querySelectorAll('[onclick*="print"]').forEach(el => {                el.removeAttribute('onclick');            });        }                // Single print function - this is the ONLY function that should call window.print()        let isPrinting = false; // Flag to prevent multiple calls        function singlePrintFunction(e) {            if (e) e.preventDefault();            if (isPrinting) return;            isPrinting = true;            try {                // All sections will be printed by default since print options are removed                // Format loan amount for printing                const loanAmount = document.getElementById('loanAmount').value;                if (loanAmount) {                    try {                        const amountInWords = convertNumberToWords(parseFloat(loanAmount));                        document.getElementById('loanAmountWords').textContent = amountInWords;                    } catch (e) {                        console.error("Error converting amount to words:", e);                        document.getElementById('loanAmountWords').textContent = "Amount in words not available";                    }                }                // Add A4 class to body for print                document.body.classList.add('a4-print');                // Trigger the print dialog after a short delay                setTimeout(function() {                    window.print();                    // Reset after printing                    setTimeout(function() {                        document.body.classList.remove('a4-print');                        isPrinting = false;                        // Clean up any temporary elements                        document.querySelectorAll('.temp-print-element').forEach(el => el.remove());                        // Reset any classes that were modified for printing                        if (customerSection && !printCustomerDetails) {                            customerSection.classList.remove('hide-for-print');                        }                        if (jewelrySection && !printJewelryDetails) {                            jewelrySection.classList.remove('hide-for-print');                        }                        signatureRows.forEach(signatureRow => {                            if (!printSignatures) {                                signatureRow.classList.remove('hide-for-print');                            }                        });                    }, 1000);                }, 100);            } catch (error) {                console.error("Error in print function:", error);                alert("There was an error with the print function. Please try again.");                isPrinting = false;            }            return false;        }    </script>        <style>        /* Images container and boxes styling */        .images-container {            color: #eb0505;            display: flex;            gap: 20px;            margin-bottom: 15px;            justify-content: center;            align-items: center;            flex-wrap: wrap;            width: 100%;        }                .image-box {            flex: 1;            border: 1px solid #ddd;            border-radius: 5px;            padding: 5px;            background-color: #f9f9f9;            color: #e70606;        }                .customer-box {            flex-basis: 20%;            max-width: 20%;            display: flex;            flex-direction: column;            gap: 10px;        }                .jewelry-box {            flex-basis: 30%;        }                .box-header {            font-weight: bold;            margin-bottom: 10px;            padding-bottom: 5px;            border-bottom: 1px solid #eee;            font-size: 14px;            margin-top: 10px;            color: #cf0808; /* Dark red color for headers */        }        .box-content {            display: flex;            flex-direction: column;            align-items: center;            margin-top: 10px;            text-align: center;            position: relative;            color: #fc0000;        }        .image-preview-container, .jewelry-placeholder {            margin-top: 10px;            text-align: center;            position: relative;            width: 120px;            height: 120px;        }        #customerPhotoPreview, .jewelry-preview-img {            width: 120px;            height: 120px;            object-fit: cover;            background-color: #fff;        }        /* Jewelry images container */        #jewelryPreviewContainer {            width: 100%;            max-width: 100%;            height: auto;            color: #000;            display: flex;            flex-wrap: wrap;            gap: 50px;            margin-top: 50px;            justify-content: center;        }        /* Print styles for the image boxes */        @media print {            .image-box {                flex: 50%;                max-width: 50%;                border: none !important;                padding: 0 !important;                background: none !important;                box-shadow: none !important;            }            .customer-box, .jewelry-box {                flex-basis: 100%;                max-width: 100%;                flex-basis: 100%;                max-width: 100%;                flex-basis: 100%;                max-width: 100%;            }            .box-header {                font-size: 14px;                margin-bottom: 10px;            }            .box-content {                display: flex;                flex-direction: column;                align-items: center;            }            .image-preview-container, .jewelry-placeholder {                width: 12px;                height: 12px;            }                     /* Make customer and jewelry preview images fit properly */            #customerPhotoPreview,            .jewelry-preview-img {                display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;            }            #jewelryPreviewContainer {                display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;            }            .images-container {                display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;                            }                        .image-box {              display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;            }        }    </style>    <script>        // Calculate net weight automatically        function calculateNetWeight() {            console.log("Calculating net weight");            const jewelryWeightInput = document.getElementById('jewelryWeight');            const stoneWeightInput = document.getElementById('stoneWeight');            const netWeightInput = document.getElementById('netWeight');            if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {                console.error("Could not find weight inputs");                return;            }            const totalWeight = parseFloat(jewelryWeightInput.value) || 0;            const stoneWeight = parseFloat(stoneWeightInput.value) || 0;            console.log(`Total weight: ${totalWeight}`);            console.log(`Stone weight: ${stoneWeight}`);            const netWeight = totalWeight - stoneWeight;            if (netWeight >= 0) {                netWeightInput.value = netWeight.toFixed(2);                console.log(`Net weight: ${netWeight.toFixed(2)}`);            } else {                console.error("Net weight is negative");            }        }        // Add event listeners for weight inputs                document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);        // Function to update amount in words           function calculateNetWeight() {            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0        }    </script>    <script>        // Calculate net weight automatically        function calculateNetWeight() {            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0;            const stoneWeight = parseFloat(document.getElementById('stoneWeight').value) || 0;            const netWeight = totalWeight - stoneWeight;            if (netWeight >= 0) {                document.getElementById('netWeight').value = netWeight.toFixed(2);            }        }        // Add event listeners for weight inputs                document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);        // Function to update amount in words           function calculateNetWeight() {            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0        }    </script>    <style>        /* General styles */        html,        body {            margin: 0;            padding: 0;            box-sizing: border-box;            font-size: 16px;            line-height: 1.5;        }        html {            height: 10%;            font-size: 16px; /* Base font size */            scroll-behavior: smooth; /* Smooth scrolling */            background-color: #f4f4f4; /* Light background color */            color: #2505b4; /* Default text color */            font-family: Arial, sans-serif;            overflow-x: hidden; /* Prevent horizontal scroll */            position: relative; /* For absolute positioning of child elements */            padding: 0;            margin: 0;        }        body {            font-family: Arial, sans-serif;            background-color: #f4f4f4;            margin: top 20px;            padding  20px;        }                .container {            position: relative;            width: 100%;            max-width: 1000px;            margin: 0 auto;            border: 1px solid #ddd;            border-radius: 5px;            padding: 20px;            background-color: #fff;            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);              background: rgb(247, 245, 245);            padding: 20px;            border-radius: 8px;            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);            position: relative;            overflow: hidden;            box-sizing: border-box;            display: flex;            flex-direction: column;            gap: 20px;            min-height: 100vh;            justify-content: space-between;            border: 1px solid #ddd;            border-radius: 5px;            padding: 20px;            background-color: #fff;            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);                    }                h1, h2 {            text-align: center;            color: #cf0808;               margin-bottom: 20px;            font-size: 24px;            font-weight: bold;            text-transform: uppercase;        }                .section-box {            margin-bottom: 10px;            padding: 8px 10px;            border: 1px solid #ddd;            border-radius: 5px;               }                .form-group {            margin-bottom: 8px;        }                .form-group label {            margin-bottom: 2px;            font-size: 13px;        }                .form-group input, .form-group textarea, .form-group select {            padding: 5px 8px;            font-size: 13px;            height: 28px;            width: calc(100% - 20px);            border-radius: 4px;            border: 1px solid #ddd;        }        .form-group select {            -webkit-appearance: none;            -moz-appearance: none;            appearance: none;            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;            background-size: 10px;        }        .image-container-box {            display: flex;            justify-content: center;            align-items: flex-start;            gap: 20px;            margin-bottom: 10px;            max-width: 800px;            margin: 0 auto 10px auto;            padding: 0 15px;        }        .image-item {            width: 220px;            min-width: 200px;            max-width: 240px;            background: #fff;            border: 1.5px solid #222;            border-radius: 10px;            padding: 15px 10px;            box-sizing: border-box;            display: flex;            flex-direction: column;            align-items: center;        }        .image-item label {            font-weight: bold;            margin-bottom: 10px;            text-align: center;            width: 100%;        }        .image-item input[type="file"] {            display: block;            margin: 0 auto 4px auto;            width: 100%;            box-sizing: border-box;            position: static;            font-size: 11px;            padding: 3px;        }        .image-item label {            font-weight: bold;            margin-bottom: 6px;            width: 100%;            text-align: left;            font-size: 16px;        }        /* Customer Details Compact Layout */        .customer-details-compact {            max-width: 800px;            margin: 0 auto;            padding: 15px;        }        .customer-details-grid {            display: grid;            grid-template-columns: 1fr 1fr;            gap: 20px;            margin-top: 10px;        }        .customer-column-left,        .customer-column-right {            display: flex;            flex-direction: column;            gap: 12px;        }        .customer-details-grid .form-group {            margin-bottom: 0;        }        .customer-details-grid .form-group label {            font-size: 14px;            margin-bottom: 4px;        }        .customer-details-grid .form-group input,        .customer-details-grid .form-group textarea {            padding: 8px;            font-size: 14px;        }        /* Responsive design for smaller screens */        @media (max-width: 768px) {            .customer-details-grid {                grid-template-columns: 1fr;                gap: 15px;            }            .customer-details-compact {                padding: 12px;            }        }        /* Loan Information Compact Layout */        .loan-info-compact {            margin-top: 20px;            padding: 15px;            background: #f9f9f9;            border-radius: 8px;            border: 1px solid #e0e0e0;        }        .loan-info-grid {            display: flex;            flex-direction: column;            gap: 15px;        }        .loan-row-top {            display: grid;            grid-template-columns: 1fr 1fr 1fr;            gap: 15px;        }        .loan-row-bottom {            display: flex;            width: 100%;        }        .amount-words-group {            width: 100%;        }        .amount-words {            background: #fff;            border: 1px solid #ddd;            border-radius: 4px;            padding: 10px;            min-height: 20px;            font-style: italic;            color: #666;        }        .loan-info-compact .form-group {            margin-bottom: 0;        }        .loan-info-compact .form-group label {            font-size: 14px;            font-weight: 600;            margin-bottom: 5px;            color: #333;        }        .loan-info-compact .form-group input {            padding: 8px;            font-size: 14px;            border: 1px solid #ccc;            border-radius: 4px;        }        /* Responsive design for loan info */        @media (max-width: 768px) {            .loan-row-top {                grid-template-columns: 1fr;                gap: 12px;            }            .loan-info-compact {                padding: 12px;            }        }        .file-info {            font-size: 10px;            color: #888;            margin-bottom: 4px;            width: 100%;            text-align: center;            border-radius: 4px;            background: #f9f9f9;            padding: 3px 0;            line-height: 1.2;        }        .image-preview-box {            margin-top: 8px;            width: 100%;            height: 120px;            min-height: 120px;            display: flex;            justify-content: center;            align-items: center;            border: 2px solid #ddd;            border-radius: 8px;            background: #fafafa;            box-shadow: 0 2px 4px rgba(161, 53, 53, 0.1);            padding: 5px;            box-sizing: border-box;            overflow: hidden;        }        #customerPhotoPreview,        .jewelry-preview-img {            max-width: 100% !important;            max-height: 100% !important;            width: auto !important;            height: auto !important;            object-fit: cover;            border-radius: 6px;            background: #fff;            display: block;            border: 1px solid #ccc;            box-shadow: 0 1px 3px rgba(0,0,0,0.2);            margin: 0 auto;        }        .image-preview-box img:hover {            transform: scale(1.05);            cursor: pointer;            transition: transform 0.2s ease;        }

        .image-preview-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
            transition: all 0.2s ease;
        }

        /* Camera Controls */
        .camera-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 6px;
            width: 100%;
        }

        .camera-controls input[type="file"] {
            width: 100%;
            font-size: 11px;
            padding: 4px;
        }

        .camera-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
            align-self: center;
            width: fit-content;
        }

        .camera-btn:hover {
            background: #0056b3;
        }

        /* Camera Modal */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .camera-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-header h3 {
            margin: 0;
            color: #333;
        }

        .close-camera {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-camera:hover {
            color: #000;
        }

        #customerCameraVideo,
        #jewelryCameraVideo {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .camera-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .capture-btn,
        .retake-btn,
        .save-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .capture-btn {
            background: #28a745;
            color: white;
        }

        .retake-btn {
            background: #ffc107;
            color: #212529;
        }

        .save-btn {
            background: #007bff;
            color: white;
        }

        .capture-btn:hover {
            background: #218838;
        }

        .retake-btn:hover {
            background: #e0a800;
        }

        .save-btn:hover {
            background: #0056b3;
        }

        /* Images Section Compact Layout */
        .images-section-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .images-section-compact h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: #333;
        }

        /* Responsive design for images section */
        @media (max-width: 768px) {
            .image-container-box {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                width: 100%;
                padding: 0 10px;
                margin: 0 auto;
                max-width: 90%;
            }

            .images-section-compact {
                padding: 12px;
            }
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
            color: #999;
            font-size: 12px;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #ad0202;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 10mm;
    }

    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    .container {
        width: 190mm !important;
        max-width: 190mm !important;
        margin: 0 auto !important;
        padding: 5mm !important;
        text-align: center !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Center all form elements */
    .section-box {
        margin: 2mm auto !important;
        padding: 2mm !important;
        text-align: left !important;
        width: 100% !important;
    }

    /* Center header */
    .header-flex {
        justify-content: center !important;
        text-align: center !important;
        margin-bottom: 3mm !important;
    }

    /* Center images section */
    .image-container-box {
        justify-content: center !important;
        margin: 0 auto !important;
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    body {
        font-size: 10pt !important; /* Adjust as needed */
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0.25in !important; /* Adjust as needed */
        border: 1px solid #ccc !important;
    }

    .hide-on-print {
        display: none !important;
        visibility: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        margin: 0 !important;
    }
        .signature-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 40px;
        width: 100%;
    }
    
    .signature-block {
        flex: 1;
        padding: 0 10px;
    }
    
    .signature-block:first-child {
        text-align: left;
    }
    .signature-block:nth-child(2) {
        text-align: center;
    }
    .signature-block:last-child {
        text-align: right;
    }
    
    @media print {
        body {
            font-size: 8px !important; /* Adjust as needed */
        }
        .container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 5mm !important; /* Adjust as needed */
        }
        .header-flex {
            margin-bottom: 3mm !important;
        }
        .header-logo img {
            width: 20mm !important;
            height: 20mm !important;
        }
        .section-box {
            margin-bottom: 2mm !important;
            padding: 2mm !important;
        }
        .form-group {
            margin-bottom: 1mm !important;
        }
        .form-group label {
            margin-bottom: 0.5mm !important;
        }
        .image-preview-box img,
        #customerPhotoPreview,
        .jewelry-preview-img {
            width: 15mm !important;
            height: 15mm !important;
        }
        .weight-row {
            gap: 1mm !important;
        }
        .weight-item {
            padding: 1mm !important;
        }
        .weight-item label {
            font-size: 7px !important;
        }
        .weight-item input {
            font-size: 7px !important;
        }
        .signature-row {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-end !important;
            margin-top: 40px !important;
            width: 100% !important;
        }
        .signature-block {
            flex: 1 !important;
            padding: 0 10px !important;
            text-align: center !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:first-child {
            text-align: left !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:nth-child(2) {
            text-align: center !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
        }
        .signature-block:last-child {
            text-align: right !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
            page-break-after: avoid !important;
            }        /* Signature row adjustments */              .signature-row {            display: flex;            justify-content: space-between;            align-items: flex-end;            margin-top: 40px;            width: 100%;            gap: 10px;        }                .signature-block {            flex: 1;            padding: 0 10px;        }                .signature-label {            font-weight: bold;            margin-bottom: 30px;            font-size: 13px;            text-align: center;        }                .signature-line {            border-bottom: 1px solid #333;            margin: 30px 0 0 0;            height: 30px;            width: 100%;            display: block;        }            @media print {            .signature-row,            .signature-block,            .signature-label,            .signature-block input[type="text"] {                display: block !important;                visibility: visible !important;                height: auto !important;                width: 100% !important;                color: #000 !important;            }            .signature-row {                position: static !important;                margin-top: 40px !important;                page-break-inside: avoid !important;            }            .signature-block input[type="text"] {                border: 1px solid #333 !important;                background: #fff !important;                margin-top: 8px !important;                width: 80% !important;                max-width: 220px !important;            }    /* Add more specific print styles here */}    </style></head><body>    <!-- Place this inside your .container at the top --><div class="header-flex header-center">    <div class="header-logo">        <img src="20250608_130850.png" alt="Amman Gold Finance Logo">    </div>    <div class="header-details">        <h1>AMMAN GOLD FINANCE</h1>        <div><b>REGISTERED OFFICE: NO 2/4-4, S.V.A. EXTENSION - 4, OPPOSITE GOVERNMENT GIRLS HIGH SCHOOL,</b></div>        <div><b>TIRUCHENGODE - 637211, NAMAKKAL DISTRICT.</b></div>        <div><b>GST NO: 33AAGFA6262H1Z3</b></div>        <div><b>REGISTERED UNDER THE TAMIL NADU PAWN BROKERS ACT,1943.</b></div>        <div><b>LICENCE NO: 2020250401145</b></div>        <div><b><E-mail>E-mail:<EMAIL></E-mail>: </b></div>    </div></div><style>.header-flex.header-center {    justify-content: center;    text-align: center;    gap: 30px;}.header-flex.header-center .header-logo {    display: flex;    justify-content: center;    align-items: center;}.header-flex.header-center .header-details {    text-align: center;    align-items: center;    display: flex;    flex-direction: column;}.header-flex.header-center .header-details h1 {    text-align: center;}.header-flex.header-center .header-details div {    text-align: center;    display: block;    width: 100%;}@media (max-width: 700px) {    .header-flex.header-center {        flex-direction: column;        gap: 10px;    }    .header-flex.header-center .header-logo img {        margin: 0 auto;    }}</style>    <div class="container">        <!-- Customer Information -->        <div class="section-box customer-details-compact">            <!-- Customer Details -->            <h2>Customer Details</h2>            <div class="customer-details-grid">                <!-- Left Column -->                <div class="customer-column-left">                    <div class="form-group">                        <label for="name">Full Name:</label>                        <input type="text" id="name" placeholder="John Doe">                    </div>                    <div class="form-group">                        <label for="address">Address:</label>                        <textarea id="address" rows="2" placeholder="123 Gold Street, City"></textarea>                    </div>                    <div class="form-group">                        <label for="pincode">Pincode:</label>                        <input type="text" id="pincode" placeholder="123456">                    </div>                    <div class="form-group">                        <label for="landmark">Landmark:</label>                        <input type="text" id="landmark" placeholder="Landmark">                    </div>                </div>                <!-- Right Column -->                <div class="customer-column-right">                    <div class="form-group">                        <label for="idProof">ID Proof:</label>                        <input type="text" id="idProof" placeholder="Aadhar Card / Voter ID / Pan Card / Passport">                    </div>                    <div class="form-group">                        <label for="email">Email:</label>                        <input type="email" id="email" placeholder="<EMAIL>">                    </div>                    <div class="form-group">                        <label for="dob">Date of Birth:</label>                        <input type="date" id="dob">                    </div>                    <div class="form-group">                        <label for="contact">Contact Number:</label>                        <input type="tel" id="contact" placeholder="1234567890">                    </div>                </div>            </div>                        <!-- Loan Information Section -->            <div class="loan-info-compact">                <div class="loan-info-grid">                    <!-- Top Row - Loan Amount and Dates -->                    <div class="loan-row-top">                        <div class="form-group">                            <label for="loanAmount">Loan Amount:</label>                            <input type="number" id="loanAmount" placeholder="0.00" min="0" step="0.01">                        </div>                        <div class="form-group">                            <label for="loanDate">Loan Date:</label>                            <input type="date" id="loanDate">                        </div>                        <div class="form-group">                            <label for="renewalDate">Last Renewal Date:</label>                            <input type="date" id="renewalDate">                        </div>                        <div class="form-group">                            <label for="Schemename">Scheme Name</label>                            <select id="Scheme Name">                                <option value="select">Select</option>                                <option value="AGL 0.90 Paise">AGL 0.90 Paise</option>                                <option value="AGL 1.00 RUPEE">AGL 1.00 "</option>                                <option value="MGL 1.25 RUPEE">MGL 1.25 </option>                                <option value="HGL 1.5 RUPEE">HGL 1.5 </option>                                <option value="HGL+ 1.80 RUPEE">HGL+ 1.80 </option>                                <option value="EGL 2.00 RUPEE">EGL 2.00 </option>                                <option value="EGL 2.10 RUPEE">EGL 2.10 </option>                                <option value="EGL+ 2.50 RUPEE">EGL+ 2.50 </option>                            </select>                        </div>                    </div>                    <!-- Bottom Row - Amount in Words -->                    <div class="loan-row-bottom">                        <div class="form-group amount-words-group">                            <label>Amount in Words:</label>                            <div id="loanAmountWords" class="amount-words"></div>                        </div>                    </div>                </div>            </div>        </div>        <!-- Image upload section -->        <div class="section-box images-section-compact">            <h2>Images</h2>            <div class="image-container-box">                <!-- Customer Photo -->                <div class="image-item">                    <label>Customer Photo:</label>                    <div class="camera-controls">                        <input type="file" id="customerPhotoInput" accept="image/*">                        <button type="button" id="customerCameraBtn" class="camera-btn">📷 Camera</button>                    </div>                    <div id="customerFileInfo" class="file-info">No file chosen</div>                    <!-- Camera Modal -->                    <div id="customerCameraModal" class="camera-modal" style="display: none;">                        <div class="camera-content">                            <div class="camera-header">                                <h3>Take Customer Photo</h3>                                <button type="button" id="customerCloseCamera" class="close-camera">×</button>                            </div>                            <video id="customerCameraVideo" autoplay playsinline></video>                            <canvas id="customerCameraCanvas" style="display: none;"></canvas>                            <div class="camera-buttons">                                <button type="button" id="customerCaptureBtn" class="capture-btn">📸 Capture</button>                                <button type="button" id="customerRetakeBtn" class="retake-btn" style="display: none;">🔄 Retake</button>                                <button type="button" id="customerSaveBtn" class="save-btn" style="display: none;">✓ Save</button>                            </div>                        </div>                    </div>                    <div class="image-preview-box">                        <div id="customerPhotoPlaceholder" class="image-placeholder hide-on-print">                            <span style="font-size: 7px;">Upload Customer Photo</span>                        </div>                        <img id="customerPhotoPreview" alt="Customer Photo" style="display: none;">                    </div>                </div>                                <!-- Jewelry Photos -->                <div class="image-item">                    <label>Jewelry Photos:</label>                    <div class="camera-controls">                        <input type="file" id="jewelryPhotoInput" accept="image/*" multiple>                        <button type="button" id="jewelryCameraBtn" class="camera-btn">📷 Camera</button>                    </div>                    <div id="jewelryFileInfo" class="file-info">No file chosen</div>                    <!-- Camera Modal -->                    <div id="jewelryCameraModal" class="camera-modal" style="display: none;">                        <div class="camera-content">                            <div class="camera-header">                                <h3>Take Jewelry Photo</h3>                                <button type="button" id="jewelryCloseCamera" class="close-camera">×</button>                            </div>                            <video id="jewelryCameraVideo" autoplay playsinline></video>                            <canvas id="jewelryCameraCanvas" style="display: none;"></canvas>                            <div class="camera-buttons">                                <button type="button" id="jewelryCaptureBtn" class="capture-btn">📸 Capture</button>                                <button type="button" id="jewelryRetakeBtn" class="retake-btn" style="display: none;">🔄 Retake</button>                                <button type="button" id="jewelrySaveBtn" class="save-btn" style="display: none;">✓ Save</button>                            </div>                        </div>                    </div>                    <div class="image-preview-box">                        <div id="jewelryPreviewContainer">                            <div id="jewelryPhotoPlaceholder" class ="image-placeholder hide-on-print">                                <span style="font-size: 7px;">Upload Jewelry Images</span>                            </div>                        </div>                    </div>                </div>            </div>        </div>        <!-- Single Jewelry Details section with proper structure -->        <div class="section-box">            <h2>Jewelry Details</h2>            <div id="jewelryItemsList">                <!-- Initial jewelry item will be added here by JavaScript -->            </div>            <!-- Totals calculation row -->              <div class="jewelry-totals">                <h3>Totals</h3>                <div class="weight-row totals-row">                    <div class="weight-item">                        <label for="totalJewelryCount">Total Jewelry Count:</label>                        <input type="number" id="totalJewelryCount" readonly>                    </div>                    <div class="weight-item">                        <label for="totalJewelryWeight">Total Weight (grams):</label>                        <input type="number" id="totalJewelryWeight" readonly>                    </div>                    <div class="weight-item">                        <label for="totalStoneWeight">Total Stone Weight (grams):</label>                        <input type="number" id="totalStoneWeight" readonly>                    </div>                    <div class="weight-item">                        <label for="totalNetWeight">Total Net Weight (grams):</label>                        <input type="number" id="totalNetWeight" readonly>                    </div>                </div>            </div>                        <div class="add-item-container">                <button type="button" id="addJewelryItemBtn" class="add-item-btn hide-on-print">Add Jewelry Item</button>            </div>        </div>              <!-- Place this after the totals section, before </div> of .container -->        <!-- Print button -->        <div class="section-box">            <button class="print-btn header-center" onclick="printDocument()">Print Document</button>              </div>        <div class="signature-row">            <div class="signature-block left">                <label class="signature-label" for="BranchManagerSignature">Branch Manager Signature</label>            </div>            <div class="signature-block center">                <label class="signature-label" for="CashierSignature">Cashier Signature</label>            </div>            <div class="signature-block right">                <label class="signature-label" for="CustomerSignature">Customer Signature</label>            </div>        </div>    </div>    <script>        // Function to add a new jewelry item        function addJewelryItem() {            const jewelryItemsList = document.getElementById('jewelryItemsList');            const itemCount = jewelryItemsList.children.length + 1;            const jewelryItemDiv = document.createElement('div');            jewelryItemDiv.className = 'jewelry-item';            jewelryItemDiv.innerHTML = `                <div class="weight-row">                    <div class="weight-item">                        <label for="jewelryCount_0">Jewelry Count:</label>                        <input type="number" id="jewelryCount_0" class="jewelry-count" min="1" value="1">                    </div>                    <div class="weight-item">                        <label for="jewelryWeight_0">Gross Weight (grams):</label>                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">                        <label for                        <input type="number" id="jewelryWeight_0" class="jewelry-weight" min="0" step="0.1">                        </div>                    <div class="weight-item">                        <label for="stoneWeight_0">Stone Weight (grams):</label>                        <input type="number" id="stoneWeight_0" class="stone-weight" min="0" step="0.1">                    </div>                    <div class="weight-item">                        <label for="netWeight_0">Net Weight (grams):</label>                        <input type="number" id="netWeight_0" class="net-weight" min="0" step="0.1" readonly>                    </div>                </div>                <div class="jewelry-type-row">                    <div class="jewelry-selection">                        <label for="jewelryType_0">Jewelry Type:</label>                        <select id="jewelryType_0" class="jewelry-type">                           <option value="">Select Jewelry Type</option>                            <option value="gold-chain">Gold Chain</option>                            <option value="gold-ring">Gold Ring</option>                            <option value="gold-bracelet">Gold Bracelet</option>                            <option value="gold-necklace">Gold Necklace</option>                            <option value="gold-earrings">Gold Earrings</option>                            <option value="gold-bangles">Gold Bangles</option>                            <option value="gold-pendants">Gold Pendants</option>                            <option value="gold-ear-studs">Gold Ear Studs</option>                            <option value="gold-studs">Gold Studs</option>                            <option value="gold-stone studs ">Gold Stone Studs</option>                            <option value="gold-mattal">Gold Mattal</option>                            <option value="gold-baby studs">Gold Baby Studs</option>                            <option value="gold-lockets">Gold Lockets</option>                            <option value="gold-anklets">Gold Anklets</option>                            <option value="gold-manga pich">Gold Manga Pich</option>                            <option value="gold-talikasu">Gold Talikasu </option>                            <option value="gold-nose pin">Gold Nose Pin</option>                            <option value="gold-thali kundu">Gold Thali Kundu</option>                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>                            <option value="gold-gold coin">Gold Coin</option>                            <option value="silver-items">Silver Items</option>                        </select>                    </div>                    <div class="purity-selection">                        <label for="purityType_0">Purity Type:</label>                        <select id="purityType_0" class="purity-type">                            <option value="">Select Purity</option>                            <option value="24k">24K (99.9%)</option>                            <option value="22k">22K (91.6%)</option>                            <option value="18k">18K (75.0%)</option>                            <option value="14k">14K (58.3%)</option>                            <option value="10k">10K (41.7%)</option>                        </select>                    </div>                                        <div class="item-actions">                        <button type="button" class="remove-item-btn hide-on-print" data-index="0" style="display: none;">Remove</button>                    </div>                </div>            `;                        jewelryItemsList.appendChild(jewelryItemDiv);                        // Initialize net weight calculation for the initial item            initializeNetWeightCalculation(0);        }                // Function to add a new jewelry item        function addJewelryItem() {            const jewelryItemsList = document.getElementById('jewelryItemsList');            if (!jewelryItemsList) return;            const itemCount = jewelryItemsList.querySelectorAll('.jewelry-item').length;            const newIndex = itemCount;            const newItem = document.createElement('div');            newItem.className = 'jewelry-item';            newItem.innerHTML = `                <div class="weight-row">                    <div class="weight-item">                        <label for="jewelryCount_${newIndex}">Jewelry Count:</label>                        <input type="number" id="jewelryCount_${newIndex}" class="jewelry-count" min="1" value="1">                    </div>                    <div class="weight-item">                        <label for="jewelryWeight_${newIndex}">Gross Weight (grams):</label>                        <input type="number" id="jewelryWeight_${newIndex}" class="jewelry-weight" min="0" step="0.1">                    </div>                    <div class="weight-item">                        <label for="stoneWeight_${newIndex}">Stone Weight (grams):</label>                        <input type="number" id="stoneWeight_${newIndex}" class="stone-weight" min="0" step="0.1">                    </div>                    <div class="weight-item">                        <label for="netWeight_${newIndex}">Net Weight (grams):</label>                        <input type="number" id="netWeight_${newIndex}" class="net-weight" min="0" step="0.1" readonly>                    </div>                </div>                <div class="jewelry-type-row">                    <div class="jewelry-selection">                        <label for="jewelryType_${newIndex}">Jewelry Type:</label>                        <select id="jewelryType_${newIndex}" class="jewelry-type">                            <option value="">Select Jewelry Type</option>                            <option value="gold-chain">Gold Chain</option>                            <option value="gold-ring">Gold Ring</option>                            <option value="gold-bracelet">Gold Bracelet</option>                            <option value="gold-necklace">Gold Necklace</option>                            <option value="gold-earrings">Gold Earrings</option>                            <option value="gold-bangles">Gold Bangles</option>                            <option value="gold-pendants">Gold Pendants</option>                            <option value="gold-ear-studs">Gold Ear Studs</option>                            <option value="gold-studs">Gold Studs</option>                            <option value="gold-stone studs ">Gold Stone Studs</option>                            <option value="gold-mattal">Gold Mattal</option>                            <option value="gold-baby studs">Gold Baby Studs</option>                            <option value="gold-lockets">Gold Lockets</option>                            <option value="gold-anklets">Gold Anklets</option>                            <option value="gold-manga pich">Gold Manga Pich</option>                            <option value="gold-talikasu">Gold Talikasu </option>                            <option value="gold-nose pin">Gold Nose Pin</option>                            <option value="gold-thali kundu">Gold Thali Kundu</option>                            <option value="gold-stud and jimikki">Gold Stud and Jimikki</option>                            <option value="gold-gold coin">Gold Coin</option>                            <option value="silver-items">Silver Items</option>                        </select>                    </div>                    <div class="purity-selection">                        <label for="purityType_${newIndex}">Purity Type:</label>                        <select id="purityType_${newIndex}" class="purity-type">                            <option value="">Select Purity</option>                            <option value="24k">24K (99.9%)</option>                            <option value="22k">22K (91.6%)</option>                            <option value="18k">18K (75.0%)</option>                            <option value="14k">14K (58.3%)</option>                            <option value="10k">10K (41.7%)</option>                        </select>                    </div>                    <div class="item-actions">                        <button type="button" class="remove-item-btn hide-on-print" data-index="${newIndex}">Remove</button>                    </div>                </div>            `;            jewelryItemsList.appendChild(newItem);            // Hide remove button only for the first item            const removeBtn = newItem.querySelector('.remove-item-btn');            if (removeBtn) {                if (newIndex === 0) {                    removeBtn.style.display = 'none';                } else {                    removeBtn.style.display = 'inline-block';                }                removeBtn.addEventListener('click', function() {                    removeJewelryItem(newIndex);                });            }            // Attach event listeners for calculation            newItem.querySelector('.jewelry-count').addEventListener('input', calculateTotals);            newItem.querySelector('.jewelry-weight').addEventListener('input', calculateTotals);            newItem.querySelector('.stone-weight').addEventListener('input', calculateTotals);            // Initialize net weight calculation for the new item            initializeNetWeightCalculation(newIndex);            // Show the remove button on the first item if we have more than one item            if (jewelryItemsList.querySelectorAll('.jewelry-item').length > 1) {                const firstItemRemoveBtn = jewelryItemsList.querySelector('.jewelry-item:first-child .remove-item-btn');                if (firstItemRemoveBtn) {                    firstItemRemoveBtn.style.display = 'inline-block';                }            }            calculateTotals();        }                // Function to remove a jewelry item        function removeJewelryItem(index) {            console.log(`Removing jewelry item with index: ${index}`);                        const jewelryItemsList = document.getElementById('jewelryItemsList');            const items = jewelryItemsList.querySelectorAll('.jewelry-item');                        // Don't remove if it's the last item            if (items.length <= 1) {                console.log("Cannot remove the last item");                return;            }                        console.log(`Total items before removal: ${items.length}`);            console.log(`Attempting to remove item with index: ${index}`);            // Check if the index is valid            if (index < 0 || index >= items.length) {                console.error(`Invalid index: ${index}. Cannot remove item.`);                return;            }            console.log(`Valid index: ${index}. Proceeding with removal.`);            // Log the current items for debugging            items.forEach((item, i) => {                console.log(`Item ${i}:`, item);            });                        console.log(`Items before removal: ${items.length}`);            // Log the remove button for debugging            const removeBtn = document.querySelector(`.remove-item-btn[data-index="${index}"]`);            if (removeBtn) {                console.log("Remove button found:", removeBtn);            } else {                console.error(`Remove button with index ${index} not found`);            }            // Log the jewelry items list for debugging            console.log("Jewelry items list:", jewelryItemsList);            // Log the items in the jewelry items list            console.log("Items in jewelry items list:", items);                console.log(`Items in jewelry items list: ${items.length}`);            // Log the jewelry items list container            console.log("Jewelry items list container:", jewelryItemsList);            // Log the current state of the jewelry items list            console.log("Current jewelry items list state:", jewelryItemsList.innerHTML);            // Find the item with the matching index and remove it            for (let i = 0; i < items.length; i++) {                const removeBtn = items[i].querySelector('.remove-item-btn');                if (removeBtn && removeBtn.getAttribute('data-index') == index) {                    items[i].remove();                    console.log(`Item with index ${index} removed`);                    break;                } else {                    console.error(`Remove button with index ${index} not found`);                    console.error(`Remove button with index ${index} not found`);                    removeBtn.classList.add('hide-on-print');                    removeBtn.style.display = 'none';                }            }                        // Hide the remove button on the first item if we only have one item left            if (jewelryItemsList.querySelectorAll('.jewelry-item').length === 1) {                const firstItemRemoveBtn = document.querySelector('.jewelry-item:first-child .remove-item-btn');                if (firstItemRemoveBtn) {                    firstItemRemoveBtn.style.display = 'none';                    console.log("First item remove button hidden");                    firstItemRemoveBtn.classList.add('hide-on-print');                } else {                    console.error("First item remove button not found");                    firstItemRemoveBtn.classList.add('hide-on-print');                    firstItemRemoveBtn.style.display = 'none';                    firstItemRemoveBtn.classList.add('hide-on-print');                                    }            }                        // Calculate totals after removing an item            calculateTotals();        }                // Function to initialize net weight calculation        function initializeNetWeightCalculation(index) {            const jewelryWeightInput = document.getElementById(`jewelryWeight_${index}`);            const stoneWeightInput = document.getElementById(`stoneWeight_${index}`);            const netWeightInput = document.getElementById(`netWeight_${index}`);                        if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {                console.error(`Could not find weight inputs for index ${index}`);                return;            }                        // Function to calculate net weight            function calculateNetWeight() {                const jewelryWeight = parseFloat(jewelryWeightInput.value) || 0;                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;                const netWeight = jewelryWeight - stoneWeight;                netWeightInput.value = netWeight > 0 ? netWeight.toFixed(1) : 0;                                // Update totals whenever an individual weight changes                calculateTotals();            }                        // Add event listeners for weight inputs            jewelryWeightInput.addEventListener('input', calculateNetWeight);            stoneWeightInput.addEventListener('input', calculateNetWeight);        }                // Function to calculate totals from all jewelry items        function calculateTotals() {            console.log("Calculating totals");            let totalJewelryCount = 0;            let totalJewelryWeight = 0;            let totalStoneWeight = 0;            let totalNetWeight = 0;                        // Get all jewelry items            const jewelryItems = document.querySelectorAll('.jewelry-item');                         // Calculate totals    jewelryItems.forEach(item => {        const jewelryCount = parseFloat(item.querySelector('.jewelry-count')?.value) || 0;        const jewelryWeight = parseFloat(item.querySelector('.jewelry-weight')?.value) || 0;        const stoneWeight = parseFloat(item.querySelector('.stone-weight')?.value) || 0;        const netWeight = parseFloat(item.querySelector('.net-weight')?.value) || 0;        totalJewelryCount += jewelryCount;        totalJewelryWeight += jewelryWeight;        totalStoneWeight += stoneWeight;        totalNetWeight += netWeight;    });                      // Update total fields    document.getElementById('totalJewelryCount').value = totalJewelryCount;    document.getElementById('totalJewelryWeight').value = totalJewelryWeight.toFixed(1);    document.getElementById('totalStoneWeight').value = totalStoneWeight.toFixed(1);    document.getElementById('totalNetWeight').value = totalNetWeight.toFixed(1);    console.log(`Totals calculated - Count: ${totalJewelryCount}, Jewelry: ${totalJewelryWeight}, Stone: ${totalStoneWeight}, Net: ${totalNetWeight}`);}                // Print functionality        function initializePrintFunctionality() {            // First, remove any existing event listeners by replacing all buttons            const printBtns = document.querySelectorAll('.print-btn');            printBtns.forEach(btn => {                // Create a completely new button                const newBtn = document.createElement('button');                newBtn.className = 'print-btn';                newBtn.textContent = 'Print Document';                newBtn.type = 'button';                newBtn.id = 'printBtn';                newBtn.style.display = 'inline-block';                newBtn.style.margin = '10px 0';                newBtn.style.padding = '10px 20px';                newBtn.style.backgroundColor = '#4CAF50';                newBtn.style.color = 'white';                newBtn.style.border = 'chocolate 1px solid';                newBtn.style.borderRadius = '5px';                newBtn.style.cursor = 'pointer';                newBtn.style.fontSize = '16px';                newBtn.style.fontWeight = 'bold';                newBtn.style.textAlign = 'center';                                // Replace the old button                if (btn.parentNode) {                    btn.parentNode.replaceChild(newBtn, btn);                }            });                        // Now add a single event listener to the new button            const newPrintBtn = document.querySelector('.print-btn');            if (newPrintBtn) {                // Use a named function for clarity                newPrintBtn.addEventListener('click', singlePrintFunction);            }                        // Remove any inline onclick attributes from the HTML            document.querySelectorAll('[onclick*="print"]').forEach(el => {                el.removeAttribute('onclick');            });        }                // Single print function - this is the ONLY function that should call window.print()        let isPrinting = false; // Flag to prevent multiple calls        function singlePrintFunction(e) {            if (e) e.preventDefault();            if (isPrinting) return;            isPrinting = true;            try {                // All sections will be printed by default since print options are removed                // Format loan amount for printing                const loanAmount = document.getElementById('loanAmount').value;                if (loanAmount) {                    try {                        const amountInWords = convertNumberToWords(parseFloat(loanAmount));                        document.getElementById('loanAmountWords').textContent = amountInWords;                    } catch (e) {                        console.error("Error converting amount to words:", e);                        document.getElementById('loanAmountWords').textContent = "Amount in words not available";                    }                }                // Add A4 class to body for print                document.body.classList.add('a4-print');                // Trigger the print dialog after a short delay                setTimeout(function() {                    window.print();                    // Reset after printing                    setTimeout(function() {                        document.body.classList.remove('a4-print');                        isPrinting = false;                        // Clean up any temporary elements                        document.querySelectorAll('.temp-print-element').forEach(el => el.remove());                        // Reset any classes that were modified for printing                        if (customerSection && !printCustomerDetails) {                            customerSection.classList.remove('hide-for-print');                        }                        if (jewelrySection && !printJewelryDetails) {                            jewelrySection.classList.remove('hide-for-print');                        }                        signatureRows.forEach(signatureRow => {                            if (!printSignatures) {                                signatureRow.classList.remove('hide-for-print');                            }                        });                    }, 1000);                }, 100);            } catch (error) {                console.error("Error in print function:", error);                alert("There was an error with the print function. Please try again.");                isPrinting = false;            }            return false;        }    </script>        <style>        /* Images container and boxes styling */        .images-container {            color: #eb0505;            display: flex;            gap: 20px;            margin-bottom: 15px;            justify-content: center;            align-items: center;            flex-wrap: wrap;            width: 100%;        }                .image-box {            flex: 1;            border: 1px solid #ddd;            border-radius: 5px;            padding: 5px;            background-color: #f9f9f9;            color: #e70606;        }                .customer-box {            flex-basis: 20%;            max-width: 20%;            display: flex;            flex-direction: column;            gap: 10px;        }                .jewelry-box {            flex-basis: 30%;        }                .box-header {            font-weight: bold;            margin-bottom: 10px;            padding-bottom: 5px;            border-bottom: 1px solid #eee;            font-size: 14px;            margin-top: 10px;            color: #cf0808; /* Dark red color for headers */        }        .box-content {            display: flex;            flex-direction: column;            align-items: center;            margin-top: 10px;            text-align: center;            position: relative;            color: #fc0000;        }        .image-preview-container, .jewelry-placeholder {            margin-top: 10px;            text-align: center;            position: relative;            width: 120px;            height: 120px;        }        #customerPhotoPreview, .jewelry-preview-img {            width: 120px;            height: 120px;            object-fit: cover;            background-color: #fff;        }        /* Jewelry images container */        #jewelryPreviewContainer {            width: 100%;            max-width: 100%;            height: auto;            color: #000;            display: flex;            flex-wrap: wrap;            gap: 50px;            margin-top: 50px;            justify-content: center;        }        /* Print styles for the image boxes */        @media print {            .image-box {                flex: 50%;                max-width: 50%;                border: none !important;                padding: 0 !important;                background: none !important;                box-shadow: none !important;            }            .customer-box, .jewelry-box {                flex-basis: 100%;                max-width: 100%;                flex-basis: 100%;                max-width: 100%;                flex-basis: 100%;                max-width: 100%;            }            .box-header {                font-size: 14px;                margin-bottom: 10px;            }            .box-content {                display: flex;                flex-direction: column;                align-items: center;            }            .image-preview-container, .jewelry-placeholder {                width: 12px;                height: 12px;            }                     /* Make customer and jewelry preview images fit properly */            #customerPhotoPreview,            .jewelry-preview-img {                display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;            }            #jewelryPreviewContainer {                display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;            }            .images-container {                display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;                            }                        .image-box {              display: flex;                flex-wrap: wrap;                gap: 10px;                margin-top: 10px;                justify-content: center;                    width: 100%;                max-width: 100%;                flex-direction: column;                align-items: center;            }        }    </style>    <script>        // Calculate net weight automatically        function calculateNetWeight() {            console.log("Calculating net weight");            const jewelryWeightInput = document.getElementById('jewelryWeight');            const stoneWeightInput = document.getElementById('stoneWeight');            const netWeightInput = document.getElementById('netWeight');            if (!jewelryWeightInput || !stoneWeightInput || !netWeightInput) {                console.error("Could not find weight inputs");                return;            }            const totalWeight = parseFloat(jewelryWeightInput.value) || 0;            const stoneWeight = parseFloat(stoneWeightInput.value) || 0;            console.log(`Total weight: ${totalWeight}`);            console.log(`Stone weight: ${stoneWeight}`);            const netWeight = totalWeight - stoneWeight;            if (netWeight >= 0) {                netWeightInput.value = netWeight.toFixed(2);                console.log(`Net weight: ${netWeight.toFixed(2)}`);            } else {                console.error("Net weight is negative");            }        }        // Add event listeners for weight inputs                document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);        // Function to update amount in words           function calculateNetWeight() {            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0        }    </script>    <script>        // Calculate net weight automatically        function calculateNetWeight() {            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0;            const stoneWeight = parseFloat(document.getElementById('stoneWeight').value) || 0;            const netWeight = totalWeight - stoneWeight;            if (netWeight >= 0) {                document.getElementById('netWeight').value = netWeight.toFixed(2);            }        }        // Add event listeners for weight inputs                document.getElementById('jewelryWeight').addEventListener('input', calculateNetWeight);        document.getElementById('stoneWeight').addEventListener('input', calculateNetWeight);        // Function to update amount in words           function calculateNetWeight() {            const totalWeight = parseFloat(document.getElementById('jewelryWeight').value) || 0        }    </script>    <style>        /* General styles */        html,        body {            margin: 0;            padding: 0;            box-sizing: border-box;            font-size: 16px;            line-height: 1.5;        }        html {            height: 10%;            font-size: 16px; /* Base font size */            scroll-behavior: smooth; /* Smooth scrolling */            background-color: #f4f4f4; /* Light background color */            color: #2505b4; /* Default text color */            font-family: Arial, sans-serif;            overflow-x: hidden; /* Prevent horizontal scroll */            position: relative; /* For absolute positioning of child elements */            padding: 0;            margin: 0;        }        body {            font-family: Arial, sans-serif;            background-color: #f4f4f4;            margin: top 20px;            padding  20px;        }                .container {            position: relative;            width: 100%;            max-width: 1000px;            margin: 0 auto;            border: 1px solid #ddd;            border-radius: 5px;            padding: 20px;            background-color: #fff;            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);              background: rgb(247, 245, 245);            padding: 20px;            border-radius: 8px;            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);            position: relative;            overflow: hidden;            box-sizing: border-box;            display: flex;            flex-direction: column;            gap: 20px;            min-height: 100vh;            justify-content: space-between;            border: 1px solid #ddd;            border-radius: 5px;            padding: 20px;            background-color: #fff;            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);                    }                h1, h2 {            text-align: center;            color: #cf0808;               margin-bottom: 20px;            font-size: 24px;            font-weight: bold;            text-transform: uppercase;        }                .section-box {            margin-bottom: 10px;            padding: 8px 10px;            border: 1px solid #ddd;            border-radius: 5px;               }                .form-group {            margin-bottom: 8px;        }                .form-group label {            margin-bottom: 2px;            font-size: 13px;        }                .form-group input, .form-group textarea, .form-group select {            padding: 5px 8px;            font-size: 13px;            height: 28px;            width: calc(100% - 20px);            border-radius: 4px;            border: 1px solid #ddd;        }        .form-group select {            -webkit-appearance: none;            -moz-appearance: none;            appearance: none;            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10"><polygon points="0,0 10,0 5,5" fill="%23222"/></svg>') no-repeat right 10px center;            background-size: 10px;        }        .image-container-box {            display: flex;            justify-content: center;            align-items: flex-start;            gap: 20px;            margin-bottom: 10px;            max-width: 800px;            margin: 0 auto 10px auto;            padding: 0 15px;        }        .image-item {            width: 220px;            min-width: 200px;            max-width: 240px;            background: #fff;            border: 1.5px solid #222;            border-radius: 10px;            padding: 15px 10px;            box-sizing: border-box;            display: flex;            flex-direction: column;            align-items: center;        }        .image-item label {            font-weight: bold;            margin-bottom: 10px;            text-align: center;            width: 100%;        }        .image-item input[type="file"] {            display: block;            margin: 0 auto 4px auto;            width: 100%;            box-sizing: border-box;            position: static;            font-size: 11px;            padding: 3px;        }        .image-item label {            font-weight: bold;            margin-bottom: 6px;            width: 100%;            text-align: left;            font-size: 16px;        }        /* Customer Details Compact Layout */        .customer-details-compact {            max-width: 800px;            margin: 0 auto;            padding: 15px;        }        .customer-details-grid {            display: grid;            grid-template-columns: 1fr 1fr;            gap: 20px;            margin-top: 10px;        }        .customer-column-left,        .customer-column-right {            display: flex;            flex-direction: column;            gap: 12px;        }        .customer-details-grid .form-group {            margin-bottom: 0;        }        .customer-details-grid .form-group label {            font-size: 14px;            margin-bottom: 4px;        }        .customer-details-grid .form-group input,        .customer-details-grid .form-group textarea {            padding: 8px;            font-size: 14px;        }        /* Responsive design for smaller screens */        @media (max-width: 768px) {            .customer-details-grid {                grid-template-columns: 1fr;                gap: 15px;            }            .customer-details-compact {                padding: 12px;            }        }        /* Loan Information Compact Layout */        .loan-info-compact {            margin-top: 20px;            padding: 15px;            background: #f9f9f9;            border-radius: 8px;            border: 1px solid #e0e0e0;        }        .loan-info-grid {            display: flex;            flex-direction: column;            gap: 15px;        }        .loan-row-top {            display: grid;            grid-template-columns: 1fr 1fr 1fr;            gap: 15px;        }        .loan-row-bottom {            display: flex;            width: 100%;        }        .amount-words-group {            width: 100%;        }        .amount-words {            background: #fff;            border: 1px solid #ddd;            border-radius: 4px;            padding: 10px;            min-height: 20px;            font-style: italic;            color: #666;        }        .loan-info-compact .form-group {            margin-bottom: 0;        }        .loan-info-compact .form-group label {            font-size: 14px;            font-weight: 600;            margin-bottom: 5px;            color: #333;        }        .loan-info-compact .form-group input {            padding: 8px;            font-size: 14px;            border: 1px solid #ccc;            border-radius: 4px;        }        /* Responsive design for loan info */        @media (max-width: 768px) {            .loan-row-top {                grid-template-columns: 1fr;                gap: 12px;            }            .loan-info-compact {                padding: 12px;            }        }        .file-info {            font-size: 10px;            color: #888;            margin-bottom: 4px;            width: 100%;            text-align: center;            border-radius: 4px;            background: #f9f9f9;            padding: 3px 0;            line-height: 1.2;        }        .image-preview-box {            margin-top: 8px;            width: 100%;            height: 120px;            min-height: 120px;            display: flex;            justify-content: center;            align-items: center;            border: 2px solid #ddd;            border-radius: 8px;            background: #fafafa;            box-shadow: 0 2px 4px rgba(161, 53, 53, 0.1);            padding: 5px;            box-sizing: border-box;            overflow: hidden;        }        #customerPhotoPreview,        .jewelry-preview-img {            max-width: 100% !important;            max-height: 100% !important;            width: auto !important;            height: auto !important;            object-fit: cover;            border-radius: 6px;            background: #fff;            display: block;            border: 1px solid #ccc;            box-shadow: 0 1px 3px rgba(0,0,0,0.2);            margin: 0 auto;        }        .image-preview-box img:hover {            transform: scale(1.05);            cursor: pointer;            transition: transform 0.2s ease;        }

        .image-preview-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
            transition: all 0.2s ease;
        }

        /* Camera Controls */
        .camera-controls {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 6px;
            width: 100%;
        }

        .camera-controls input[type="file"] {
            width: 100%;
            font-size: 11px;
            padding: 4px;
        }

        .camera-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
            align-self: center;
            width: fit-content;
        }

        .camera-btn:hover {
            background: #0056b3;
        }

        /* Camera Modal */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .camera-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-header h3 {
            margin: 0;
            color: #333;
        }

        .close-camera {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-camera:hover {
            color: #000;
        }

        #customerCameraVideo,
        #jewelryCameraVideo {
            width: 100%;
            max-width: 400px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .camera-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .capture-btn,
        .retake-btn,
        .save-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .capture-btn {
            background: #28a745;
            color: white;
        }

        .retake-btn {
            background: #ffc107;
            color: #212529;
        }

        .save-btn {
            background: #007bff;
            color: white;
        }

        .capture-btn:hover {
            background: #218838;
        }

        .retake-btn:hover {
            background: #e0a800;
        }

        .save-btn:hover {
            background: #0056b3;
        }

        /* Images Section Compact Layout */
        .images-section-compact {
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
        }

        .images-section-compact h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: #333;
        }

        /* Responsive design for images section */
        @media (max-width: 768px) {
            .image-container-box {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                width: 100%;
                padding: 0 10px;
                margin: 0 auto;
                max-width: 90%;
            }

            .images-section-compact {
                padding: 12px;
            }
        }

        .image-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border: 1px dashed #ddd;
            border-radius: 6px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            width: 100%;
            height: 100%;
            color: #999;
            font-size: 12px;
        }

        .image-placeholder:hover {
            border-color: #999;
        }

        .image-placeholder i {
            font-size: 24px;
            color: #999;
            margin-bottom: 10px;
        }

        .image-placeholder span {
            font-size: 14px;
            color: #999;
        }
        .image-placeholder span:hover {
            color: #333;
        }
                /* Add to your <style> section */
        .header-flex {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        .header-logo img {
            width: 120px;
            height: 120px;
            object-fit: contain;
            border-radius: 8px;
            border: 1px solid #eee;
            background: #fff;
        }
        .header-details {
            flex: 1;
            text-align: left;
        }
        .header-details h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 1px;
            color: #1900f8;
            text-transform: uppercase;
        }
        .header-details div {
            font-size: 1rem;
            margin-bottom: 4px;
            color: #ad0202;
            font-weight: bold;
            letter-spacing: 1px;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
               @media print {
    /* Reset page size and margins */
    @page {
        size: A4;
        margin: 10mm;
    }

    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 8px !important;
    }

    .container {
        width: 190mm !important;
        max-width: 190mm !important;
        margin: 0 auto !important;
        padding: 5mm !important;
        text-align: center !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Center all form elements */
    .section-box {
        margin: 2mm auto !important;
        padding: 2mm !important;
        text-align: left !important;
        width: 100% !important;
    }

    /* Center header */
    .header-flex {
        justify-content: center !important;
        text-align: center !important;
        margin-bottom: 3mm !important;
    }

    /* Center images section */
    .image-container-box {
        justify-content: center !important;
        margin: 0 auto !important;
    }
    /* Force single page */
   
    * {
        overflow: visible !important;
        page-break-inside: avoid !important;
        page-break-before: avoid !important;
        page-break-after: avoid !important;
    }

    /* Hide unnecessary elements */
    .hide-on-print,
    .print-options,
    input[type="file"],
    button:not(.print-btn) {
        display: none !important;
    }

    /* Compact spacing */
    h1, h2 {
        margin: 1mm 0 !important;
        font-size: 10
        px !important;
    }

    input, select, textarea {
        padding: 1mm !important;
        margin: 0.5mm 0 !important;
        height: auto !important;
    }

    .signature-row {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 !important;
        padding: 0 !important;
        gap: 0 !important;
        min-height: 0 !important;
    }
    .signature-block {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        width: 33.33% !important;
        max-width: 33.33% !important;
        min-width: 0 !important;
        padding: 0 15px !important;
        box-sizing: border-box !important;
        height: auto !important;
    }
    .signature-block:first-child {
        text-align: left !important;
        padding-left: 0 !important;
        align-items: flex-start !important;
    }
    .signature-block:last-child {
        text-align: right !important;
        padding-right: 0 !important;
        align-items: flex-end !important;   
    }
    .signature-block img {
        max-width: 100% !important;
        height: auto !important;
        margin: 0 auto !important;
    }
    .signature-block p {
        font-size: 8px !important;
        margin: 0 !important;
        text-align: center !important;
        color: #333 !important;
    }
    .signature-block p span {
        font-weight: bold !important;
        color: #000 !important;
    }
}
        </style>
// Logout function - Fixed version
function logout() {
    console.log('Logout button clicked'); // Debug log
    
    // Show confirmation dialog
    const confirmLogout = confirm('Are you sure you want to logout?\n\nAny unsaved changes will be lost.');
    
    if (confirmLogout) {
        console.log('User confirmed logout'); // Debug log
        
        // Clear all session and local storage
        sessionStorage.clear();
        localStorage.clear();
        
        // Clear any cookies if they exist
        document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
        });
        
        // Show logout message
        const userInfoBar = document.querySelector('.user-info-bar');
        if (userInfoBar) {
            userInfoBar.innerHTML = '<div style="text-align: center; width: 100%; color: white; font-weight: bold;">Logging out...</div>';
        }
        
        // Force redirect to login page
        setTimeout(() => {
            console.log('Redirecting to login page'); // Debug log
            window.location.replace('login.html'); // Use replace instead of href
        }, 500);
    } else {
        console.log('User cancelled logout'); // Debug log
    }
}
// Alternative logout function if the above doesn't work
function forceLogout() {
    sessionStorage.clear();
    localStorage.clear();
    window.location.href = 'login.html';
}
// Make sure the function is globally available
window.logout = logout;
window.forceLogout = forceLogout;
    </script>
</body>
</html>