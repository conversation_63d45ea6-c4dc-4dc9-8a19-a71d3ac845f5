// Logout function - Fixed version
function logout() {
    console.log('Logout button clicked'); // Debug log
    
    // Show confirmation dialog
    const confirmLogout = confirm('Are you sure you want to logout?\n\nAny unsaved changes will be lost.');
    
    if (confirmLogout) {
        console.log('User confirmed logout'); // Debug log
        
        // Clear all session and local storage
        sessionStorage.clear();
        localStorage.clear();
        
        // Clear any cookies if they exist
        document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
        });
        
        // Show logout message
        const userInfoBar = document.querySelector('.user-info-bar');
        if (userInfoBar) {
            userInfoBar.innerHTML = '<div style="text-align: center; width: 100%; color: white; font-weight: bold;">Logging out...</div>';
        }
        
        // Force redirect to login page
        setTimeout(() => {
            console.log('Redirecting to login page'); // Debug log
            window.location.replace('login.html'); // Use replace instead of href
        }, 500);
    } else {
        console.log('User cancelled logout'); // Debug log
    }
}

// Alternative logout function if the above doesn't work
function forceLogout() {
    sessionStorage.clear();
    localStorage.clear();
    window.location.href = 'login.html';
}
    

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Finance</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="shortcut icon" href="20250608_130850.png" type="image/icon">
</head>
<body>
    <button id="logoutButton">Logout</button>
    <script src="logout.js"></script>
</body>
</html>
    </body>
    </html>
<!DOCTYPE html>
<html lang="en">
<head>
