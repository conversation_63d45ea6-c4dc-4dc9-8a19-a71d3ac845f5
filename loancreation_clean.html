
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loan Creation - Amman Gold Finance</title>
    <link rel="shortcut icon" href="20250608_130850.png" type="image/x-icon">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 999;
        }

        .sidebar-header {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 30px;
        }

        .sidebar-header img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            color: #ecf0f1;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #3498db;
        }

        .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: white;
            border-left-color: #3498db;
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
        }

        .content-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .content-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }

        /* Container */
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 1200px;
            margin: 0 auto;
            overflow: hidden;
            padding: 20px;
        }

        /* Customer Details */
        .customer-details-compact {
            background: #f9f9f9;
            margin: 0 auto;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .customer-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 10px;
        }

        .customer-column-left,
        .customer-column-right {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            box-sizing: border-box;
        }

        /* Print buttons */
        .print-buttons-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .print-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .office-copy {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .office-copy:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .customer-copy {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .customer-copy:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        /* Print Styles */
        @media print {
            .sidebar {
                display: none !important;
            }
            
            .main-content {
                margin-left: 0 !important;
            }

            .print-btn, .print-buttons-container {
                display: none !important;
            }

            /* Customer address positioning based on copy type */
            .customer-copy-print .customer-details-grid {
                grid-template-columns: 1fr 1fr !important;
            }

            .customer-copy-print .customer-column-left {
                order: 1 !important;
            }

            .customer-copy-print .customer-column-right {
                order: 2 !important;
            }

            .office-copy-print .customer-details-grid {
                grid-template-columns: 1fr 1fr !important;
            }

            .office-copy-print .customer-column-left {
                order: 2 !important;
            }

            .office-copy-print .customer-column-right {
                order: 1 !important;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="20250608_130850.png" alt="Logo">
            <h3>AMMAN GOLD FINANCE</h3>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="main.html#loan-section" class="nav-link active">
                    <span class="nav-icon">💰</span>
                    Loan Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="interestcreation.html" class="nav-link">
                    <span class="nav-icon">📊</span>
                    Interest Creation
                </a>
            </li>
            <li class="nav-item">
                <a href="Termsandcondition.html" class="nav-link">
                    <span class="nav-icon">📋</span>
                    Terms And Condition
                </a>
            </li>
            <li class="nav-item">
                <a href="javascript:void(0);" onclick="logout(); return false;" class="nav-link">
                    <span class="nav-icon">🚪</span>
                    Logout
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <h1>AMMAN GOLD FINANCE<br>
            REGISTERED OFFICE: NO 2/4-4, S.V.A. EXTENSION - 4,<br>
            OPPOSITE GOVERNMENT GIRL'S HIGH SCHOOL,<br>
            TIRUCHENGODE - 637211, NAMAKKAL DISTRICT.<br>
            Phone: +91 8608183335<br>
            Email: <EMAIL></h1>
        </div>

        <div class="container">
            <!-- Customer Information -->
            <div class="customer-details-compact">
                <h2>Customer Details</h2>
                <div class="customer-details-grid">
                    <!-- Left Column -->
                    <div class="customer-column-left">
                        <div class="form-group">
                            <label for="customerId">Customer ID:</label>
                            <input type="text" id="customerId" placeholder="CUST-001">
                        </div>
                        <div class="form-group">
                            <label for="name">Full Name:</label>
                            <input type="text" id="name" placeholder="John Doe">
                        </div>
                        <div class="form-group">
                            <label for="address">Address:</label>
                            <textarea id="address" rows="2" placeholder="123 Gold Street, City"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="pincode">Pincode:</label>
                            <input type="text" id="pincode" placeholder="123456">
                        </div>
                        <div class="form-group">
                            <label for="gender">Gender:</label>
                            <select id="gender">
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="customer-column-right">
                        <div class="form-group">
                            <label for="idProof">ID Proof:</label>
                            <input type="text" id="idProof" placeholder="Aadhar Card / Voter ID / Pan Card / Passport">
                        </div>
                        <div class="form-group">
                            <label for="dob">Date of Birth:</label>
                            <input type="date" id="dob">
                        </div>
                        <div class="form-group">
                            <label for="contact">Contact Number:</label>
                            <input type="tel" id="contact" placeholder="1234567890">
                        </div>
                        <div class="form-group">
                            <label for="landmark">Landmark:</label>
                            <input type="text" id="landmark" placeholder="Landmark">
                        </div>
                        <div class="form-group">
                            <label for="occupation">Occupation:</label>
                            <input type="text" id="occupation" placeholder="Occupation">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Print buttons -->
            <div class="print-buttons-container">
                <button class="print-btn office-copy" onclick="printDocument('office')">Office Copy</button>
                <button class="print-btn customer-copy" onclick="printDocument('customer')">Customer Copy</button>
            </div>
        </div>
    </div>

    <script>
        // Print function for both office and customer copies
        let isPrinting = false;
        
        function printDocument(copyType) {
            if (isPrinting) return;
            isPrinting = true;

            try {
                // Add copy type class to body for print
                document.body.classList.add('a4-print');
                if (copyType === 'office') {
                    document.body.classList.add('office-copy-print');
                } else if (copyType === 'customer') {
                    document.body.classList.add('customer-copy-print');
                }

                // Trigger the print dialog after a short delay
                setTimeout(function() {
                    window.print();

                    // Reset after printing
                    setTimeout(function() {
                        document.body.classList.remove('a4-print', 'office-copy-print', 'customer-copy-print');
                        isPrinting = false;
                    }, 1000);
                }, 100);
            } catch (error) {
                console.error("Error in print function:", error);
                alert("There was an error with the print function. Please try again.");
                isPrinting = false;
            }
            return false;
        }

        // Logout function
        function logout() {
            if (confirm("Are you sure you want to logout?")) {
                window.location.href = "/logout";
            }
            return false;
        }
    </script>
</body>
</html>

